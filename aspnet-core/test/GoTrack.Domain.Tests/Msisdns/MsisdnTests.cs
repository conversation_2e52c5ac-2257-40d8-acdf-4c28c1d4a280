using System;
using Shouldly;
using Xunit;

namespace GoTrack.Msisdns;

public class MsisdnTests : GoTrackDomainTestBase
{


    [Fact]
    public void ShouldToStringCorrectly()
    {
        var msisdn = new Msisdn("963", "099", "6346666");
        msisdn.ToString().ShouldBe("00963996346666");
    }

    [Fact]
    public void EqualsAsValueObject()
    {
        var msisdn1 = new Msisdn("940", "000", "5550100");
        var msisdn2 = new Msisdn("940", "000", "5550100");
        if (! msisdn1.ValueEquals(msisdn2))
        {
            throw new ShouldAssertException("Value object comparision failed");
        }
    }

}