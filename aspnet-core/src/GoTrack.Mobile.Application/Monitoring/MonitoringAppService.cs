using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Mobile.Monitoring.DTOs.DeviceHistory;
using GoTrack.Mobile.Monitoring.DTOs.LiveLocations;
using GoTrack.Observations.ObservationViewModels;
using GoTrack.UserTrackAccountAssociations;
using GoTrack.VehicleDeviceEventLogs;
using GoTrack.Vehicles;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Features;
using Warp10Abstraction.Models;
using Warp10Abstraction.WarpLibs;

namespace GoTrack.Mobile.Monitoring;

public class MonitoringAppService : GoTrackMobileAppService, IMonitoringAppService
{
    private readonly IWarpLib _warpLib;
    private readonly IRepository<VehicleDeviceEventLog, Guid> _vehicleDeviceEventLogRepository;
    private readonly IRepository<Vehicle, Guid> _vehicleRepository;
    private readonly IRepository<UserTrackAccountAssociation, Guid> _userTrackAccountAssociationRepository;
    private readonly IObservationReadRepository _observationReadRepository;

    public MonitoringAppService(
        IWarpLib warpLib,
        IRepository<VehicleDeviceEventLog, Guid> vehicleDeviceEventLogRepository,
        IRepository<Vehicle, Guid> vehicleRepository,
        IRepository<UserTrackAccountAssociation, Guid> userTrackAccountAssociationRepository,
        IObservationReadRepository observationReadRepository)
    {
        _warpLib = warpLib;
        _vehicleDeviceEventLogRepository = vehicleDeviceEventLogRepository;
        _vehicleRepository = vehicleRepository;
        _userTrackAccountAssociationRepository = userTrackAccountAssociationRepository;
        _observationReadRepository = observationReadRepository;
    }


    [Authorize]
    [TrackAccountOrObserverAuthorize]
    [RequiresFeature(GoTrackFeatureDefinitions.LiveMonitoring)]
    public async Task<List<LiveLocationDto>> PostLiveLocationAsync(LiveLocationInputDto inputDto)
    {
        await CheckVehiclesAccessToObserverAsync(inputDto);

        var uniqueVehicleIds = inputDto.VehicleIds.Distinct().ToList();
        await ValidateVehiclesAsync(uniqueVehicleIds);

        var lastVehicleLogs = (await _vehicleDeviceEventLogRepository.WithDetailsAsync(log => log.Device))
            .Where(log => inputDto.VehicleIds.Contains(log.VehicleId) && log.EventName == EventName.Installed)
            .OrderByDescending(log => log.CreationTime)
            .ToList()
            .GroupBy(log => log.VehicleId)
            .ToDictionary(g => g.Key, g => g.First());

        var imeis = lastVehicleLogs.Values
            .Select(log => log.Device.Imei)
            .ToList();

        if (imeis.Count != lastVehicleLogs.Count)
        {
            throw new UserFriendlyException(L[GoTrackDomainErrorCodes.OneOrMoreVehiclesDoNotHaveADevice]);
        }

        var lastRecords = await _warpLib.GetLastRecordOfImeisAsync(imeis, inputDto.onlySpeed);
        List<LiveLocationDto> liveLocationDtos = lastRecords.Select(record => new LiveLocationDto
        {
            Imei = record.Key,
            WarpGtses = ObjectMapper.Map<List<WarpGTS>, List<WarpGTSDto>>([.. record.Value])
        }).ToList();

        var vehicleIdWithImeiDtos = lastVehicleLogs.Values.Select(log =>
            new
            {
                VehicleId = log.VehicleId,
                Imei = log.Device.Imei
            }).ToList();

        liveLocationDtos = liveLocationDtos.Join(
            vehicleIdWithImeiDtos,
            location => location.Imei,
            vehicle => vehicle.Imei,
            (location, vehicle) => new LiveLocationDto
            {
                Imei = location.Imei,
                WarpGtses = location.WarpGtses,
                VehicleId = vehicle.VehicleId
            }).ToList();

        return liveLocationDtos;
    }


    [Authorize]
    [TrackAccountOrObserverAuthorize]
    [RequiresFeature(GoTrackFeatureDefinitions.ShowVehiclePath)]
    public virtual async Task<List<WarpGtsHistoryDto>> GetVehicleHistoryAsync(VehicleHistoryInputDto inputDto)
    {
        await CheckVehicleAccessAsync(inputDto);
        
        await _vehicleRepository.GetAsync(inputDto.VehicleId);

        var lastImeiOnVehicle = (await _vehicleDeviceEventLogRepository.GetQueryableAsync())
            .Where(log => log.VehicleId == inputDto.VehicleId && log.EventName == EventName.Installed)
            .OrderByDescending(e => e.CreationTime)
            .Select(log => log.Device.Imei)
            .FirstOrDefault() 
                ?? throw new UserFriendlyException(L[GoTrackDomainErrorCodes.NoInstalledDeviceFoundForVehicle]);

        var warpGtses = await _warpLib.GetDeviceHistoryAsync(
            lastImeiOnVehicle,
            inputDto.FromDate.UtcDateTime,
            inputDto.ToDate.UtcDateTime,
            inputDto.OnlySpeed
        );

        var warpGtsHistoryDtos = ObjectMapper
            .Map<List<WarpGTS>, List<WarpGtsHistoryDto>>([.. warpGtses]);
        
        return warpGtsHistoryDtos;
    }

    private async Task ValidateVehiclesAsync(List<Guid> uniqueVehicleIds)
    {
        var existingVehicles = await _vehicleRepository.GetListAsync(vehicle => uniqueVehicleIds.Contains(vehicle.Id));
        if (existingVehicles.Count != uniqueVehicleIds.Count)
        {
            throw new UserFriendlyException(L[GoTrackDomainErrorCodes.OneOrMoreVehiclesNotFound]);
        }
    }

    private async Task CheckVehiclesAccessToObserverAsync(LiveLocationInputDto inputDto)
    {
        var trackAccountId = CurrentTrackAccount.GetId();

        var userTrackAccountAssociation = await _userTrackAccountAssociationRepository
            .GetAsync(x =>
                x.TrackAccountId == trackAccountId &&
                x.UserId == CurrentUser.Id &&
                x.Status == UserTrackAccountAssociationStatus.Active
            );

        if (userTrackAccountAssociation.AssociationType is not AssociationType.Observer)
            return;

        var vehilces = await _observationReadRepository.GetObserverVehiclesIdsAsync(
            userTrackAccountAssociation.Id
        );

        var hasAccess = inputDto.VehicleIds.All(vehilces.Contains);
        if (!hasAccess)
            throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.NoAccess]);
    }

    private async Task CheckVehicleAccessAsync(VehicleHistoryInputDto inputDto)
    {
        var trackAccountId = CurrentTrackAccount.GetId();

        var userTrackAccountAssociation = await _userTrackAccountAssociationRepository
            .GetAsync(x =>
                x.TrackAccountId == trackAccountId &&
                x.UserId == CurrentUser.Id &&
                x.Status == UserTrackAccountAssociationStatus.Active
            );

        if (userTrackAccountAssociation.AssociationType is not AssociationType.Observer)
            return;

        var vehilces = await _observationReadRepository.GetObserverVehiclesIdsAsync(
            userTrackAccountAssociation.Id
        );

        var hasAccess = vehilces.Contains(inputDto.VehicleId);
        if (!hasAccess)
            throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.NoAccess]);
    }
}