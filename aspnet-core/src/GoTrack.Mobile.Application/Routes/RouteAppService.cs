using GoTrack.Mobile.Routes.DTOs;
using GoTrack.Mobile.StopPoints.DTOs;
using GoTrack.Routes;
using GoTrack.Routes.RouteViewModels;
using GoTrack.StopPoints;
using GoTrack.TrackAccounts;
using Microsoft.AspNetCore.Authorization;
using NetTopologySuite;
using NetTopologySuite.Geometries;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Features;
using Volo.Abp.Validation;

namespace GoTrack.Mobile.Routes;

[RequiresFeature(GoTrackFeatureDefinitions.PathManagement)]
public class RouteAppService : GoTrackMobileAppService, IRouteAppService
{
    private readonly IRouteManager _routeManager;
    private readonly ICurrentTrackAccount _currentTrackAccount;
    private readonly IRepository<Route, Guid> _routeRepository;
    private readonly IRepository<StopPoint, Guid> _stopPointRepository;
    private readonly IRouteReadRepository _routeReadRepository;

    public RouteAppService(
        IRouteManager routeManager,
        ICurrentTrackAccount currentTrackAccount,
        IRepository<Route, Guid> routeRepository,
        IRepository<StopPoint, Guid> stopPointRepository,
        IRouteReadRepository routeReadRepository)
    {
        _routeManager = routeManager;
        _currentTrackAccount = currentTrackAccount;
        _routeRepository = routeRepository;
        _stopPointRepository = stopPointRepository;
        _routeReadRepository = routeReadRepository;
    }

    [Authorize]
    [TrackAccountAuthorize]
    public virtual async Task<RouteDto> CreateAsync(CreateRouteDto createRouteDto)
    {
        var trackAccountId = _currentTrackAccount.GetId();

        var gf = NtsGeometryServices.Instance.CreateGeometryFactory();

        var newLine = gf.CreateLineString(createRouteDto.Line.Select(p => new Coordinate(x: p.LongitudeX, y: p.LatitudeY)).ToArray());
        if (!newLine.IsValid)
            throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.InvalidLineString]);

        var firstPoint = newLine.Coordinates.First();
        var lastPoint = newLine.Coordinates.Last();

        var startPoint = gf.CreatePoint(new Coordinate(x: firstPoint.X, y: firstPoint.Y));
        var endPoint = gf.CreatePoint(new Coordinate(x: lastPoint.X, y: lastPoint.Y));

        var color = TryGetColor(createRouteDto.HexColor);

        var stopPoints = new List<StopPoint>();
        foreach(var stopPointDto in createRouteDto.StopPoints)
        {
            var point = gf.CreatePoint(new Coordinate(x: stopPointDto.Point.LongitudeX, y: stopPointDto.Point.LatitudeY));
            if (!point.IsValid)
                throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.InvalidStopPoint]);

            var pointColor = TryGetColor(stopPointDto.HexColor);

            stopPoints.Add(new StopPoint(GuidGenerator.Create(), stopPointDto.Name, point, pointColor, trackAccountId));
        }

        var route = await _routeManager.CreateAsync(createRouteDto.Name, newLine, color, startPoint, endPoint, trackAccountId, stopPoints);
    
        return ObjectMapper.Map<Route, RouteDto>(route);
    }

    [Authorize]
    [TrackAccountAuthorize]
    [RequiresFeature(GoTrackFeatureDefinitions.StopPointManagement)]
    public virtual async Task<RouteDto> AddStopPointToRouteAsync(Guid id, CreateStopPointDto createStopPointDto)
    {
        var trackAccountId = _currentTrackAccount.GetId();

        var gf = NtsGeometryServices.Instance.CreateGeometryFactory();

        var color = TryGetColor(createStopPointDto.HexColor);

        var point = gf.CreatePoint(new Coordinate(x: createStopPointDto.Point.LongitudeX, y: createStopPointDto.Point.LatitudeY));
        if (!point.IsValid)
            throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.InvalidStopPoint]);

        var stopPoint = new StopPoint(GuidGenerator.Create(), createStopPointDto.Name, point, color, trackAccountId);

        var route = await _routeManager.AddStopPointAsync(id, stopPoint);

        return ObjectMapper.Map<Route, RouteDto>(route);
    }

    [Authorize]
    [TrackAccountOrObserverAuthorize]
    public async Task<PagedResultDto<RouteDto>> GetListAsync(PagedResultRequestDto input)
    {
        var query = await _routeRepository.GetQueryableAsync();

        var totalCount = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(input);

        var routes = await AsyncExecuter.ToListAsync(query);

        var routeDtos = ObjectMapper.Map<List<Route>, List<RouteDto>>(routes);

        return new PagedResultDto<RouteDto>(totalCount, routeDtos);
    }

    [Authorize]
    [TrackAccountOrObserverAuthorize]
    public async Task<RouteViewModelDto> GetAsync(Guid id)
    {
        var route = await _routeReadRepository.GetRouteViewModelAsync(id);

        return ObjectMapper.Map<RouteViewModel, RouteViewModelDto>(route);
    }

    [Authorize]
    [TrackAccountOrObserverAuthorize]
    [RequiresFeature(GoTrackFeatureDefinitions.StopPointManagement)]
    public async Task<PagedResultDto<StopPointDto>> GetStopPointsOnRouteAsync(Guid id, PagedResultRequestDto input)
    {
        var route = await _routeRepository.GetAsync(id);

        var query = await _stopPointRepository.GetQueryableAsync();

        var stopPointIdsInRoute = route.StopPoints.Select(x => x.StopPointId).ToList();

        query = query.Where(x => stopPointIdsInRoute.Contains(x.Id));

        var totalCount = await AsyncExecuter.CountAsync(query);

        query = query.PageBy(input);

        var stopPoints = await AsyncExecuter.ToListAsync(query);

        var stopPointDtos = ObjectMapper.Map<List<StopPoint>, List<StopPointDto>>(stopPoints);

        return new PagedResultDto<StopPointDto>(totalCount, stopPointDtos);
    }

    [Authorize]
    [TrackAccountAuthorize]
    [RequiresFeature(GoTrackFeatureDefinitions.StopPointManagement)]
    public async Task RemoveStopPointFromRouteAsync(Guid id, Guid stopPointId)
    {
        await _routeRepository.GetAsync(id);

        var stopPoint = await _stopPointRepository.GetAsync(stopPointId);

        if (await _routeRepository.AnyAsync(x => x.Id == id && x.StopPoints.Any(x => x.StopPointId == stopPointId)) is false)
            throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.ThisStopPointIsNotAssociatedToThisRoute]);

        await _stopPointRepository.DeleteAsync(stopPoint);
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task DeleteAsync(Guid id)
    {
        var route = await _routeRepository.GetAsync(id);

        await _routeRepository.DeleteAsync(route);
    }

    private Color TryGetColor(string color)
    {
        try
        {
            return ColorTranslator.FromHtml(color);
        }
        catch (Exception)
        {
            throw new AbpValidationException("",
                [
                    new(L[GoTrackApplicationErrorCodes.InvalidColor])
                ]
            );
        }
    }
}
