using FluentValidation;
using GoTrack.Localization;
using GoTrack.Mobile.AlertDefinitions.ZoneAlertDefinitions.EnteringZoneAlertDefinitions.DTOs;
using Microsoft.Extensions.Localization;
using System.Collections.Generic;
using System.Linq;

namespace GoTrack.Mobile.AlertDefinitions.ZoneAlertDefinitions.EnteringZoneAlertDefinitions.Validators;

public class CreateEnteringZoneAlertDefinitionDtoValidator : AbstractValidator<CreateEnteringZoneAlertDefinitionDto>
{
    private readonly IStringLocalizer<GoTrackResource> _localizer;

    public CreateEnteringZoneAlertDefinitionDtoValidator(IStringLocalizer<GoTrackResource> localizer)
    {
        _localizer = localizer;

        RuleFor(x => x.VehicleIds)
            .Must(x => !x.IsNullOrEmpty())
            .When(x => x.VehicleGroupIds.IsNullOrEmpty())
            .Must(x => x.Count == x.ToHashSet().Count)
            .WithName(_localizer["GoTrack:VehicleIds"]);

        RuleFor(x => x.VehicleGroupIds)
            .Must(x => !x.IsNullOrEmpty())
            .When(x => x.VehicleIds.IsNullOrEmpty())
            .Must(x => x.Count == x.ToHashSet().Count)
            .WithName(_localizer["GoTrack:VehicleGroupIds"]);

        RuleFor(x => x.NotificationMethods)
            .Must(x => !x.IsNullOrEmpty())
            .WithName(_localizer["GoTrack:NotificationMethods"]);

        RuleFor(x => x.GeoZoneIds)
            .Must(x => !x.IsNullOrEmpty())
            .WithName(_localizer["GoTrack:GeoZoneIds"]);
    }
}
