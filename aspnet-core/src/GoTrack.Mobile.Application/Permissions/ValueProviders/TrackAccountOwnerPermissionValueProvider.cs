using System;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.TrackAccounts;
using GoTrack.UserTrackAccountAssociations;
using Microsoft.Extensions.Caching.Distributed;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Caching;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;

namespace GoTrack.Mobile.Permissions.ValueProviders;

public class TrackAccountOwnerPermissionValueProvider : PermissionValueProvider
{
    private readonly ICurrentTrackAccount _currentTrackAccount;
    private readonly IRepository<UserTrackAccountAssociation, Guid> _userTrackAccountAssociationRepository;
    private readonly ICurrentUser _currentUser;
    private readonly IDistributedCache<BoolWrapper> _cache; 

    public TrackAccountOwnerPermissionValueProvider(
        IPermissionStore permissionStore, 
        ICurrentTrackAccount currentTrackAccount, 
        IRepository<UserTrackAccountAssociation, Guid> userTrackAccountAssociationRepository, 
        ICurrentUser currentUser, IDistributedCache<BoolWrapper> cache) 
        : base(permissionStore)
    {
        _currentTrackAccount = currentTrackAccount;
        _userTrackAccountAssociationRepository = userTrackAccountAssociationRepository;
        _currentUser = currentUser;
        _cache = cache;
    }
    
    public override string Name => "TrackAccountOwner";

    public override async Task<PermissionGrantResult> CheckAsync(PermissionValueCheckContext context)
    {
        var isOwner = await IsUserTrackAccountOwnerAsync();
        return isOwner ? PermissionGrantResult.Granted : PermissionGrantResult.Undefined;
    }

    public override async Task<MultiplePermissionGrantResult> CheckAsync(PermissionValuesCheckContext context)
    {
        var permissionNames = context.Permissions.Select(x => x.Name).Distinct().ToArray();
        var isOwner = await IsUserTrackAccountOwnerAsync();

        return isOwner
            ? new MultiplePermissionGrantResult(permissionNames, PermissionGrantResult.Granted) 
            : new MultiplePermissionGrantResult(permissionNames);
    }

    private async Task<bool> IsUserTrackAccountOwnerAsync()
    {
        if (!_currentUser.IsAuthenticated || _currentTrackAccount.Id is null)
        {
            return false;
        }

        var userId = _currentUser.Id!.Value;
        var trackAccountId = _currentTrackAccount.Id.Value;
        var cacheKey = $"TrackAccountOwner_{userId}_{trackAccountId}";

        var isOwner = await _cache.GetOrAddAsync(cacheKey,
            async () =>
            {
                return new BoolWrapper(await _userTrackAccountAssociationRepository.AnyAsync(x =>
                    x.TrackAccountId == trackAccountId &&
                    x.UserId == userId &&
                    x.AssociationType == AssociationType.Owner
                ));
            },
            () => new DistributedCacheEntryOptions {
                AbsoluteExpiration = DateTimeOffset.Now.AddHours(1)
            });

        return isOwner?.Value ?? false;
    }
}

public class BoolWrapper(bool value)
{
    public bool Value { get; } = value;
}
