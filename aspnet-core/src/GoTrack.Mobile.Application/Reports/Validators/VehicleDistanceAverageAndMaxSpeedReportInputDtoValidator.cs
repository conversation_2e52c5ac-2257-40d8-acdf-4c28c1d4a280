using FluentValidation;
using GoTrack.Localization;
using GoTrack.Mobile.Reports.DTO;
using Microsoft.Extensions.Localization;

namespace GoTrack.Mobile.Reports.Validators;

public class VehicleDistanceAverageAndMaxSpeedReportInputDtoValidator : AbstractValidator<VehicleDistanceAverageAndMaxSpeedReportInputDto>
{
    private readonly IStringLocalizer<GoTrackResource> _localizer;

    public VehicleDistanceAverageAndMaxSpeedReportInputDtoValidator(IStringLocalizer<GoTrackResource> localizer)
    {
        _localizer = localizer;
        
        RuleFor(x => x.FromDate)
            .NotEmpty()
            .LessThan(x => x.ToDate)
            .WithName(_localizer["GoTrack:FromDate"]);

        RuleFor(x => x.ToDate)
            .NotEmpty()
            .WithName(_localizer["GoTrack:ToDate"]);

        RuleFor(x => x.IgnoreSpeedUnder)
            .Must(speed => speed >= 0)
            .WithName(_localizer["GoTrack:IgnoreSpeedUnder"]);
        
    }
}
