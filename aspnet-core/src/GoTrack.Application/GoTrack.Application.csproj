<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>GoTrack</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\GoTrack.Domain\GoTrack.Domain.csproj" />
    <ProjectReference Include="..\GoTrack.Application.Contracts\GoTrack.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Account.Application" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.FluentValidation" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.Identity.Application" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Application" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.TenantManagement.Application" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application" Version="8.3.0" />
  </ItemGroup>

</Project>
