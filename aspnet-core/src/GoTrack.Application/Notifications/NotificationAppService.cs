using GoTrack.Notifications.DTOs;
using GoTrack.Notifications.NotificationFactories.TestEmailNotifications;
using GoTrack.Notifications.NotificationFactories.TestFCMNotifications;
using System.Threading.Tasks;
using Volo.Abp.EventBus.Distributed;

namespace GoTrack.Notifications;

public class NotificationAppService : GoTrackAppService, INotificationAppService
{
    protected IDistributedEventBus DistributedEventBus =>
        LazyServiceProvider.LazyGetRequiredService<IDistributedEventBus>();

    protected TestFCMNotificationFactory TestFCMNotificationFactory =>
        LazyServiceProvider.LazyGetRequiredService<TestFCMNotificationFactory>();

    protected TestEmailNotificationFactory TestEmailNotificationFactory =>
        LazyServiceProvider.LazyGetRequiredService<TestEmailNotificationFactory>();

    public async Task TestFCMAsync(TestFCMRequest input)
    {
        var eto = await TestFCMNotificationFactory.CreateAsync(
            new TestFCMNotificationDataModel
            {
                Title = input.Title,
                Body = input.Body,
            },
            userId: input.UserId
        );

        await DistributedEventBus.PublishAsync(eto);
    }

    public async Task TestEmailAsync(TestEmailRequest input)
    {
        var eto = await TestEmailNotificationFactory.CreateAsync(
            new TestEmailNotificationDataModel
            {
                Subject = input.Subject,
                Body = input.Body,
            },
            userId: input.UserId
        );

        await DistributedEventBus.PublishAsync(eto);
    }
}
