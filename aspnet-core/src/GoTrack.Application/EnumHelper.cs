using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Validation;

namespace GoTrack;

public static class EnumHelper
{
    public static T GetEnumValueByName<T>(string name) where T : struct, Enum
    {
        if (Enum.TryParse(name, true, out T result))
            return result;

        throw new AbpValidationException("", new List<ValidationResult>()
        {
            new($"Invalid name '{name}' for enum type '{typeof(T).Name}'")
        });
    }
}