using System;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Permissions;
using GoTrack.Settings.DTOs;
using GoTrack.TrackAccounts.TrackAccountSubscriptions.Settings;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.SettingManagement;
using Volo.Abp.Settings;

namespace GoTrack.Settings;

public class GoTrackSettingsAppService : GoTrackAppService, IGoTrackSettingsAppService
{
    private readonly ISettingManager _settingManager;
    private readonly ISettingProvider _settingProvider;

    public GoTrackSettingsAppService(
        ISettingManager settingManager,
        ISettingProvider settingProvider)
    {
        _settingManager = settingManager;
        _settingProvider = settingProvider;
    }

    [Authorize(GoTrackPermissions.TrackAccountSubscriptionExpireReminderGet)]
    public virtual async Task<TrackAccountSubscriptionExpireReminderDto> GetTrackAccountSubscriptionExpireReminderAsync()
    {
        var gracePeriod = await _settingProvider.GetAsync(
            GoTrackSettingKeys.GracePeriod,
            GoTrackSettingKeys.GracePeriodDefaultValue);

        var notificationDaysString = await _settingProvider.GetOrNullAsync(
            GoTrackSettingKeys.NotificationDays);

        var notificationDays =
            notificationDaysString?.Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(int.Parse)
                .ToList()
            ?? GoTrackSettingKeys.NotificationDaysDefaultValue;

        return new TrackAccountSubscriptionExpireReminderDto
        {
            GracePeriod = gracePeriod,
            NotificationDays = notificationDays 
        };
    }

    [Authorize(GoTrackPermissions.TrackAccountSubscriptionExpireReminderUpdate)]
    public virtual async Task UpdateTrackAccountSubscriptionExpireReminderAsync(UpdateTrackAccountSubscriptionExpireReminderDto input)
    {
        await _settingManager.SetForCurrentTenantAsync(
            GoTrackSettingKeys.GracePeriod,
            input.GracePeriod.ToString());

        await _settingManager.SetForCurrentTenantAsync(
            GoTrackSettingKeys.NotificationDays,
            string.Join(",", input.NotificationDays));
    }

    [Authorize(GoTrackPermissions.SmsBundleExpireReminderGet)]
    public virtual async Task<SmsBundleExpireReminderDto> GetSmsBundleExpireReminderAsync()
    {
        var smsLowCountThreshold = await _settingProvider.GetAsync(
            GoTrackSettingKeys.SmsLowCountThreshold,
            GoTrackSettingKeys.SmsLowCountThresholdDefaultValue);

        return new SmsBundleExpireReminderDto
        {
            SmsLowCountThreshold = smsLowCountThreshold
        };
    }

    [Authorize(GoTrackPermissions.SmsBundleExpireReminderUpdate)]
    public virtual async Task UpdateSmsBundleExpireReminderAsync(UpdateSmsBundleExpireReminderDto input)
    {
        await _settingManager.SetForCurrentTenantAsync(
            GoTrackSettingKeys.SmsLowCountThreshold,
            input.SmsLowCountThreshold.ToString());
    }
}
