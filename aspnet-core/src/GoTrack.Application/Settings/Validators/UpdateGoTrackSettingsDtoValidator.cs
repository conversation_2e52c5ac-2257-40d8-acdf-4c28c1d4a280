using FluentValidation;
using GoTrack.Localization;
using GoTrack.Settings.DTOs;
using Microsoft.Extensions.Localization;

namespace GoTrack.Settings.Validators;

public class UpdateTrackAccountSubscriptionExpireReminderDtoValidator : AbstractValidator<UpdateTrackAccountSubscriptionExpireReminderDto>
{
    public UpdateTrackAccountSubscriptionExpireReminderDtoValidator(IStringLocalizer<GoTrackResource> localizer)
    {
        RuleFor(x => x.GracePeriod)
            .GreaterThanOrEqualTo(0)
            .WithMessage(localizer[GoTrackDomainErrorCodes.GracePeriodMustBeNonNegative]);

        RuleForEach(x => x.NotificationDays)
            .GreaterThan(0)
            .WithMessage(localizer[GoTrackDomainErrorCodes.NotificationDaysMustBePositive]);
    }
}
