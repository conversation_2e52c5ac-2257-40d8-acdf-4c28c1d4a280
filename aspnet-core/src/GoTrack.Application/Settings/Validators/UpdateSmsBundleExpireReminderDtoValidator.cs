using FluentValidation;
using GoTrack.Localization;
using GoTrack.Settings.DTOs;
using Microsoft.Extensions.Localization;

namespace GoTrack.Settings.Validators;

public class UpdateSmsBundleExpireReminderDtoValidator : AbstractValidator<UpdateSmsBundleExpireReminderDto>
{
    public UpdateSmsBundleExpireReminderDtoValidator(IStringLocalizer<GoTrackResource> localizer)
    {
        RuleFor(x => x.SmsLowCountThreshold)
            .GreaterThan(0)
            .WithMessage(localizer[GoTrackDomainErrorCodes.SmsLowCountThresholdMustBePositive]);
    }
}
