using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mime;
using Microsoft.OpenApi.Any;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace GoTrack.Mobile.SwaggerHelpers;

public class DocumentFilter : IDocumentFilter
{
    public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
    {
        // var nonMobileRoutes = swaggerDoc.Paths
        //     .Where(
        //         x => x.Key.ToLower().StartsWith("/api/abp") ||
        //              x.Key.ToLower().StartsWith("/api/account")).ToList();
        // nonMobileRoutes.ForEach(x => { swaggerDoc.Paths.Remove(x.Key); });
        
        // var _remove = swaggerDoc.Components.Schemas
            // .Where(x => x.Key.ToLower().StartsWith("volo.abp")).ToList();
        // _remove.ForEach(x => { swaggerDoc.Components.Schemas.Remove(x.Key); });
        
        swaggerDoc.Paths.Add("/connect/token", OpenIdConnectTokenOtpGrant());
    }
    
    private OpenApiPathItem OpenIdConnectTokenOtpGrant()
    {
        var x = new OpenApiSchema();
        var pathItem = new OpenApiPathItem
        {
            Operations = new Dictionary<OperationType, OpenApiOperation>
            {
                [OperationType.Post] = new OpenApiOperation
                {
                    Description = "Request a Token using the extended OTP Grant, or the default OIDC grants",
                    RequestBody = new OpenApiRequestBody
                    {
                        UnresolvedReference = false,
                        Reference = null,
                        Description = null,
                        Required = false,
                        Content = new Dictionary<string, OpenApiMediaType>()
                        {
                            ["application/json"] = new OpenApiMediaType
                            {
                                Schema = new OpenApiSchema()
                                {
                                    
                                    
                                    
                                },
                                Example = new OpenApiObject(),
                                Examples = null,
                                Encoding = null,
                                Extensions = null
                            }
                        },
                        Extensions = null
                    },
                    Responses = new OpenApiResponses
                    {
                        ["200"] = new OpenApiResponse
                        {
                            Description = "OK"
                        }
                    }
                }
            }
        };
        return pathItem;
    }

}