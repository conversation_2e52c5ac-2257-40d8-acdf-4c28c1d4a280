using System;
using GoTrack.TrackAccounts;
using Volo.Abp;
using Volo.Abp.DependencyInjection;

namespace GoTrack;

public class CurrentTrackAccount : ICurrentTrackAccount, IScopedDependency
{
    public Guid? Id { get; private set; }

    public Guid GetId()
    {
        throw new NotImplementedException();
    }

    public IDisposable Change(Guid? id) => SetCurrent(id);

    private IDisposable SetCurrent(Guid? trackAccounttId)
    {
        Id = trackAccounttId;

        //TODO check if "static (state)" necessarily
        return new DisposeAction<Guid?>((state) =>
        {
            Id = state;

        }, Id);
    }
}