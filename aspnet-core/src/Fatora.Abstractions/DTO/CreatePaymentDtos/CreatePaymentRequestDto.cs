using Fatora.Abstractions.Enums;
using Fatora.Abstractions.Exceptions;

namespace Fatora.Abstractions.DTO.CreatePaymentDtos;

public class CreatePaymentRequestDto
{
    public string Language { get; private set; }
    public int Amount { get; private set; }
    public string? CallBackUrl { get; private set; }
    public string? TriggerUrl { get; private set; }
    public string? SavedCards { get; private set; }
    public Guid? UserIdentifier { get; private set; }
    public string? Notes { get; private set; }

    public CreatePaymentRequestDto(FatoraLanguage language, int amount, string? callBackUrl,
        string? triggerUrl, bool savedCards, Guid? userIdentifier, string? notes)
    {
        if (savedCards && userIdentifier == null)
            throw new FatoraInvalidDataException("User Identifier required when saved cards true");

        Language = GetLanguage(language);
        Amount = amount;
        CallBackUrl = callBackUrl;
        TriggerUrl = triggerUrl;
        SavedCards = savedCards ? "S" : "O";
        //SavedCards = savedCards ? 1 : 0;
        //SavedCards = savedCards ? 1 : null;
        UserIdentifier = userIdentifier;
        Notes = notes;
    }

    private string GetLanguage(FatoraLanguage language)
    {
        switch (language)
        {
            case FatoraLanguage.English:
                return "en";
            case FatoraLanguage.Arabic:
                return "ar";
            default:
                return "en";
        }
    }
}