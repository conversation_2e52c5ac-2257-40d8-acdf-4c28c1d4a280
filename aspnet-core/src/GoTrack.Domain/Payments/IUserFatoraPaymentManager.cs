using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Services;

namespace GoTrack.Payments;

public interface IUserFatoraPaymentManager : IDomainService
{
    Task<UserFatoraPayment> CheckPaymentStatusAsync(Guid userFatoraPaymentId);
    Task<string> PayAsync(Guid requestId, Guid userId, string language, int amount, bool savedCards, string? callBackUrl, string? notes);
    Task ReversalPaymentAsync(Guid userId, Guid paymentId, string language);
    Task<string> PayMultipleAsync(List<Guid> requestIds, Guid userId, string language, bool savedCards, string? callBackUrl, string? notes);
}
