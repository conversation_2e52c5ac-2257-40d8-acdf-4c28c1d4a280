using System;
using Volo.Abp;
using GoTrack;

namespace GoTrack.Payments.Bills;

public abstract class BillPlanInput
{
    public Guid RequestId { get; protected set; }

    protected BillPlanInput(Guid requestId)
    {
        RequestId = requestId;
    }

    protected BillPlanInput()
    {
        // For deserialization
    }

    protected virtual void Validate()
    {
        if (RequestId == Guid.Empty)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.RequestIdRequired);
        }
    }
}