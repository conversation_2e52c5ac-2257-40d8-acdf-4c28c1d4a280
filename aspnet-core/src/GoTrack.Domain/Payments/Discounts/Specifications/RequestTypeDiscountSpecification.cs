using System;
using System.Collections.Generic;
using GoTrack.Payments.Bills;
using GoTrack.Requests;
using Volo.Abp;

namespace GoTrack.Payments.Discounts.Specifications;

public class RequestTypeDiscountSpecification : IDiscountSpecification
{
    public DiscountSpecificationKey SpecificationKey => DiscountSpecificationKey.RequestType;

    public bool IsSatisfiedBy(DiscountCriteria criteria, BillPlan billPlan)
    {
        if (criteria.DiscountSpecificationKey != SpecificationKey)
            return false;

        if (billPlan.DiscountSpecificationData.GetValueOrDefault(DiscountDataKeys.RequestType) is null)
        {
            return false;
        }

        Enum.TryParse<RequestType>(billPlan.DiscountSpecificationData[DiscountDataKeys.RequestType], out var requestType);
        Enum.TryParse<RequestType>(criteria.SpecificationValue, out var requiredRequestType);

        return requestType == requiredRequestType;
    }

    public void CheckIfValidAsync(string data)
    {
        if (Enum.TryParse<RequestType>(data, out _) is false)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DiscountSpecificationRequestTypeInvalid);
        }
    }
}