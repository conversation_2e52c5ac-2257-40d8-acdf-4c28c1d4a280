using System.Linq;
using GoTrack.Payments.Bills;
using GoTrack.Payments.PricingItems;
using Volo.Abp;

namespace GoTrack.Payments.Discounts.Specifications;

public class UserCountDiscountSpecification : IDiscountSpecification
{
    public DiscountSpecificationKey SpecificationKey => DiscountSpecificationKey.UserCount;

    public bool IsSatisfiedBy(DiscountCriteria criteria, BillPlan billPlan)
    {
        if (criteria.DiscountSpecificationKey != SpecificationKey)
            return false;

        var requestUserCount = billPlan.BillLineItems.FirstOrDefault(x => x.PricingItemKey == PricingItemKeys.AdditionalUsers)?.Quantity;

        if (requestUserCount is null)
        {
            return false;
        }

        var requiredUserCount = int.Parse(criteria.SpecificationValue);


        return requestUserCount >= requiredUserCount;
    }

    public void CheckIfValidAsync(string data)
    {
        if (int.TryParse(data, out var userCount) && userCount > 0 is false)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DiscountSpecificationUserCountInvalid);
        }
    }
}