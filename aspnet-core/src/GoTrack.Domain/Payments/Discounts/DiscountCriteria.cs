using System.Collections.Generic;
using Volo.Abp.Domain.Values;

namespace GoTrack.Payments.Discounts;

public class DiscountCriteria : ValueObject
{
    public DiscountSpecificationKey DiscountSpecificationKey { get; private set; }
    public string SpecificationValue { get; private set; }

    internal DiscountCriteria(DiscountSpecificationKey discountSpecificationKey, string specificationValue)
    {
        DiscountSpecificationKey = discountSpecificationKey;
        SpecificationValue = specificationValue;
    }

    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return DiscountSpecificationKey;
        yield return SpecificationValue;
    }
}