using System.Collections.Generic;
using Volo.Abp;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;

namespace GoTrack.Payments.PricingItems;

public class PricingItemContext : IPricingItemContext
{
    public Dictionary<string, PricingItemDefinition> PricingItems { get; }

    public PricingItemContext()
    {
        PricingItems = new Dictionary<string, PricingItemDefinition>();
    }

    public PricingItemDefinition AddPricingItem(string key, PricingType pricingType, ILocalizableString displayName, bool isNeedToSetPrice = true)
    {
        Check.NotNull(key, nameof(key));

        if (PricingItems.ContainsKey(key))
        {
            throw new AbpException($"There is already an existing permission group with key: {key}");
        }

        return PricingItems[key] = new PricingItemDefinition
        (
            key,
            pricingType,
            displayName,
            isNeedToSetPrice
        );
    }
}