using GoTrack.SeedWork;
using System;
using System.Threading.Tasks;
using Volo.Abp.Domain.Services;

namespace GoTrack.Devices;

public interface IDeviceManager : IDomainService
{
    Task<bool> CheckDeviceAvailabilityAsync(Guid id);
    Task<Device> CreateAsync(LocalizedString model, LocalizedString brand, string imei, string sim, Protocol protocol);
    Task SetDeviceActiveAsync(Guid id);
    Task SetDeviceDeactiveAsync(Guid id);
}
