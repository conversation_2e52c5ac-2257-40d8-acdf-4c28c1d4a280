using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace GoTrack.Options;

public class FirebaseOptions : IValidatableObject
{
    public string Type { get; set; }
    public string ProjectId { get; set; }
    public string PrivateKeyId { get; set; }
    public string PrivateKey { get; set; }
    public string ClientEmail { get; set; }
    public string ClientId { get; set; }
    public string TokenUri { get; set; }
    public string UniverseDomain { get; set; }
    
    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (string.IsNullOrWhiteSpace(Type))
            yield return new ValidationResult("Type is required.", [nameof(Type)]);

        if (string.IsNullOrWhiteSpace(ProjectId))
            yield return new ValidationResult("ProjectId is required.", [nameof(ProjectId)]);

        if (string.IsNullOrWhiteSpace(PrivateKey))
            yield return new ValidationResult("PrivateKey is required.", [nameof(PrivateKey)]);

        if (string.IsNullOrWhiteSpace(ClientEmail))
            yield return new ValidationResult("ClientEmail is required.", [nameof(ClientEmail)]);

        if (string.IsNullOrWhiteSpace(ClientId))
            yield return new ValidationResult("ClientId is required.", [nameof(ClientId)]);

        if (string.IsNullOrWhiteSpace(TokenUri))
            yield return new ValidationResult("TokenUri is required.", [nameof(TokenUri)]);
    }
}