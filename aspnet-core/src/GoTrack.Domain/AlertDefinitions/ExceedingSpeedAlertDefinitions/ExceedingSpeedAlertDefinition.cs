using System;
using System.Collections.Generic;
using Volo.Abp;

namespace GoTrack.AlertDefinitions.ExceedingSpeedAlertDefinitions;

public class ExceedingSpeedAlertDefinition : AlertDefinition
{
    public decimal MaxSpeed { get; private set; }

    private ExceedingSpeedAlertDefinition() : base() { }

    internal ExceedingSpeedAlertDefinition(
        Guid id,
        decimal maxSpeed,
        Guid trackAccountId,
        List<AlertDefinitionNotificationMethod> notificationMethods
        ) : base(id, trackAccountId, notificationMethods, AlertType.ExceedingSpeed)
    {
        MaxSpeed = Check.Range(maxSpeed, nameof(MaxSpeed), 1);
    }
}
