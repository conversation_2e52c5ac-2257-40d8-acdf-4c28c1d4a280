using System.Collections.Generic;
using System.Linq;
using GoTrack.MimeTypes;
using Volo.Abp;
using Volo.Abp.Domain.Services;

namespace GoTrack.BlobValidator;

public class BlobValidator : DomainService,IBlobValidator
{
    private IFileTypeInspector FileTypeInspector =>
        LazyServiceProvider.LazyGetRequiredService<IFileTypeInspector>();
    
    private const int MaxSizeMegaBytes = 2;
    
    public void ValidateImage(byte[] fileContent)
    {
        var availableImagesExtensions = MimeDetective.Definitions.DefaultDefinitions.FileTypes.Images
            .All()
            .Where(x => x.File.MimeType is not null)
            .Select(x => x.File.MimeType!)
            .Distinct()
            .ToList();

        var mimeType = FileTypeInspector.GetMimeType(fileContent);
        var validExtension = availableImagesExtensions.Any(supportedImageFormats => supportedImageFormats == mimeType);
        
        if (!validExtension)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidImageFormat).WithData("SupportedFormats", availableImagesExtensions.JoinAsString(","));
        }
        
        if (fileContent.Length > (MaxSizeMegaBytes * 1024 * 1024))
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InvalidImageSize).WithData("MaxSizeMB", MaxSizeMegaBytes);
        }
    }
}