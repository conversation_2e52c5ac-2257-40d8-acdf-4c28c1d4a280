using GoTrack.Devices;
using GoTrack.Payments.Bills;
using GoTrack.Requests.AccountSubscriptionRequests;
using GoTrack.VehicleDeviceEventLogs;
using GoTrack.Vehicles.LicensePlates;
using GoTrack.Vehicles;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Payments.PromoCodes;
using Volo.Abp;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Identity;
using GoTrack.TrackAccounts;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;

namespace GoTrack.Requests.AddVehiclesRequests;

public class AddVehiclesRequestManager : DomainService, IBillableRequestManager<AddVehiclesRequest>
{
    private readonly IRepository<AddVehiclesRequest, Guid> _addVehiclesRequestRepository;

    protected IIdentityUserRepository IdentityUserRepository =>
        LazyServiceProvider.GetRequiredService<IIdentityUserRepository>();

    protected BillManager BillManager =>
        LazyServiceProvider.GetRequiredService<BillManager>();

    protected IRepository<Device, Guid> DeviceRepository =>
        LazyServiceProvider.GetRequiredService<IRepository<Device, Guid>>();

    protected IDeviceManager DeviceManager =>
        LazyServiceProvider.GetRequiredService<IDeviceManager>();

    protected ITrackAccountRepository TrackAccountRepository =>
        LazyServiceProvider.GetRequiredService<ITrackAccountRepository>();

    protected IVehicleManager VehicleManager =>
        LazyServiceProvider.GetRequiredService<IVehicleManager>();

    protected IRepository<Vehicle, Guid> VehicleRepository =>
        LazyServiceProvider.GetRequiredService<IRepository<Vehicle, Guid>>();

    protected IVehicleDeviceEventLogManager DeviceEventLogManager =>
        LazyServiceProvider.GetRequiredService<IVehicleDeviceEventLogManager>();

    protected TrackAccountSubscriptionManager TrackAccountSubscriptionManager =>
        LazyServiceProvider.GetRequiredService<TrackAccountSubscriptionManager>();

    protected AddVehiclesRequestBillPlanFactory AddVehiclesRequestBillPlanFactory =>
        LazyServiceProvider.LazyGetRequiredService<AddVehiclesRequestBillPlanFactory>();

    protected PromoCodeManager PromoCodeManager =>
        LazyServiceProvider.LazyGetRequiredService<PromoCodeManager>();

    public AddVehiclesRequestManager(IRepository<AddVehiclesRequest, Guid> addVehiclesRequestRepository)
    {
        _addVehiclesRequestRepository = addVehiclesRequestRepository;
    }

    public async Task<AddVehiclesRequest> CreateAsync(
        Guid ownerId,
        TrackerInstallationLocation trackerInstallationLocation,
        List<SubscriptionVehicleInfo> subscriptionVehicleInfos,
        Guid trackAccountId,
        bool hasValidDevice,
        string? promoCode)
    {
        await IdentityUserRepository.GetAsync(ownerId);

        if (!string.IsNullOrEmpty(promoCode))
        {
            await PromoCodeManager.ValidatePromoCodeAsync(promoCode);
        }

        var currentTrackAccountSubscription =
            await TrackAccountSubscriptionManager.GetCurrentActiveTrackAccountSubscriptionAsync(trackAccountId);

        var newAddVehicleRequest = new AddVehiclesRequest(
            GuidGenerator.Create(),
            ownerId,
            subscriptionVehicleInfos,
            trackerInstallationLocation,
            trackAccountId,
            hasValidDevice,
            currentTrackAccountSubscription.Id,
            promoCode
        );

        var billId = await BillManager.CreateBillAsync(await GenerateBillPlan(newAddVehicleRequest),true);
        newAddVehicleRequest.SetBillId(billId);

        await _addVehiclesRequestRepository.InsertAsync(newAddVehicleRequest, true);

        return newAddVehicleRequest;
    }

    public async Task InstallDevicesAsync(Guid id, List<DeviceInstallationRequest> deviceRequests)
    {
        var request = await _addVehiclesRequestRepository.GetAsync(id);

        var distinctDeviceRequests = deviceRequests
            .GroupBy(v => new { v.Serial, v.LicensePlateSubClass })
            .Select(g => g.First())
            .ToList();

        if (distinctDeviceRequests.Count != deviceRequests.Count)
            throw new BusinessException(GoTrackDomainErrorCodes.DuplicateEntriesFoundForLicensePlateSerialAndSubClass);

        if (distinctDeviceRequests.Count != request.TrackVehicles.Count)
            throw new BusinessException(GoTrackDomainErrorCodes.TheNumberOfVehicleAndDeviceDoesNotMatchTheNumberOfRequest);

        var trackAccount = await TrackAccountRepository.GetAsync(request.TrackAccountId);

        request.SetStageAsDeviceInstallation();

        foreach (var deviceRequest in deviceRequests)
        {
            var device = await DeviceRepository.GetAsync(deviceRequest.DeviceId);

            var matchingVehicle = request.TrackVehicles.SingleOrDefault(subscriptionVehicle =>
                subscriptionVehicle.LicensePlate.Serial == deviceRequest.Serial &&
                subscriptionVehicle.LicensePlate.SubClass == deviceRequest.LicensePlateSubClass
            );

            if (matchingVehicle is null)
                throw new BusinessException(GoTrackDomainErrorCodes.SomeLicensePlateDoNotMatchWithRequest);

            if (matchingVehicle.NeedsTrackingDevice)
            {
                if (device.OwnerId is not null)
                    throw new BusinessException(GoTrackDomainErrorCodes.DeviceAlreadyOwned);

                if (await DeviceManager.CheckDeviceAvailabilityAsync(device.Id) is false)
                    throw new BusinessException(GoTrackDomainErrorCodes.TheDeviceStatusMustBeInoperative);
            }

            if (matchingVehicle.NeedsTrackingDevice is false)
            {
                if (device.OwnerId != request.OwnerId)
                    throw new BusinessException(GoTrackDomainErrorCodes.TrackingDeviceOwnerMismatch);

                if (device.Status is not DeviceStatus.ConnectedAndDeactive)
                    throw new BusinessException(GoTrackDomainErrorCodes.DeviceStatusInvalidForNonTracking);
            }

            matchingVehicle.SetDeviceId(deviceRequest.DeviceId);

            if (matchingVehicle.NeedsTrackingDevice)
            {
                device.SetOwnerId(request.OwnerId);
                await DeviceManager.SetDeviceActiveAsync(device.Id);
                await DeviceRepository.UpdateAsync(device);
            }

            var vehicle = await VehicleManager.CreateAsync(
                trackAccount.Id,
                new LicensePlate(matchingVehicle.LicensePlate.SubClass, matchingVehicle.LicensePlate.Serial),
                matchingVehicle.Color, matchingVehicle.ConsumptionRate
            );

            await VehicleRepository.InsertAsync(vehicle);

            await DeviceEventLogManager.CreateAsync(
                vehicle.Id,
                matchingVehicle.DeviceId ?? throw new EntityNotFoundException(typeof(Device), matchingVehicle.DeviceId),
                EventName.Installed
            );
        }

        await _addVehiclesRequestRepository.UpdateAsync(request);
    }


    public async Task FinishProcessingAsync(Guid id, string? note)
    {
        var subscriptionRequest = await _addVehiclesRequestRepository.GetAsync(id);

        if (!string.IsNullOrEmpty(note))
        {
            subscriptionRequest.AddNote(
                new RequestNote(
                    GuidGenerator.Create(),
                    subscriptionRequest.Id,
                    note
                )
            );
        }

        subscriptionRequest.FinishProcessing();
        subscriptionRequest.SetStageAsFinish();

        await _addVehiclesRequestRepository.UpdateAsync(subscriptionRequest);
    }

    public async Task HandlePaymentCompletionAsync(Guid requestId)
    {
        var request = await _addVehiclesRequestRepository.GetAsync(requestId);

        request.SetStageAsPaymentReview();
    }

    public async Task<BillPlan> GenerateBillPlan(AddVehiclesRequest request)
    {
        var currentTrackAccountSubscription =
            await TrackAccountSubscriptionManager.GetCurrentActiveTrackAccountSubscriptionAsync(request.TrackAccountId);

        var remainingMonths =
            await TrackAccountSubscriptionManager.GetRemainingMonths(currentTrackAccountSubscription.Id);

        var trackAccountSubscription =await TrackAccountSubscriptionManager.GetCurrentActiveTrackAccountSubscriptionAsync(request.TrackAccountId);

        return await AddVehiclesRequestBillPlanFactory.GenerateBillPlan(
            new AddVehiclesRequestBillPlanInput(
                request.Id,
                request.OwnerId,
                request.TrackVehicles.Count(x => x.NeedsTrackingDevice),
                request.TrackVehicles.Count,
                remainingMonths,
                trackAccountSubscription.SubscriptionPlan,
                request.PromoCode
            )
        );
    }
}
