using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Addresses;
using GoTrack.Devices;
using GoTrack.Payments.Bills;
using GoTrack.Payments.Discounts;
using GoTrack.Payments.PromoCodes;
using GoTrack.SubscriptionPlans;
using GoTrack.TrackAccounts;
using GoTrack.TrackAccounts.BusinessTrackAccounts;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using GoTrack.VehicleDeviceEventLogs;
using GoTrack.Vehicles;
using GoTrack.Vehicles.LicensePlates;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Uow;

namespace GoTrack.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;

public class BusinessAccountSubscriptionRequestManager : DomainService, IBusinessAccountSubscriptionRequestManager
{
    private readonly IRepository<BusinessAccountSubscriptionRequest, Guid> _businessAccountSubscriptionRequestRepository;

    protected IRepository<Vehicle, Guid> VehicleRepository
        => LazyServiceProvider.GetRequiredService<IRepository<Vehicle, Guid>>();

    protected IRepository<Device, Guid> DeviceRepository
        => LazyServiceProvider.GetRequiredService<IRepository<Device, Guid>>();

    protected IRepository<TrackAccountSubscription, Guid> TrackAccountSubscriptionRepository
        => LazyServiceProvider.GetRequiredService<IRepository<TrackAccountSubscription, Guid>>();

    protected IRepository<BusinessTrackAccount, Guid> BusinessTrackAccountRepository
        => LazyServiceProvider.GetRequiredService<IRepository<BusinessTrackAccount, Guid>>();

    protected IVehicleManager VehicleManager
        => LazyServiceProvider.GetRequiredService<IVehicleManager>();

    protected IVehicleDeviceEventLogManager DeviceEventLogManager
        => LazyServiceProvider.GetRequiredService<IVehicleDeviceEventLogManager>();

    protected IBusinessTrackAccountManager BusinessTrackAccountManager
        => LazyServiceProvider.GetRequiredService<IBusinessTrackAccountManager>();

    protected IDeviceManager DeviceManager => LazyServiceProvider.GetRequiredService<IDeviceManager>();

    protected TrackAccountSubscriptionManager TrackAccountSubscriptionManager
        => LazyServiceProvider.GetRequiredService<TrackAccountSubscriptionManager>();

    protected BillManager BillManager => LazyServiceProvider.GetRequiredService<BillManager>();

    protected DiscountManager DiscountManager => LazyServiceProvider.GetRequiredService<DiscountManager>();
    protected IRepository<Bill, Guid> BillRepository => LazyServiceProvider.GetRequiredService<IRepository<Bill, Guid>>();
    protected IUnitOfWorkManager UnitOfWorkManager => LazyServiceProvider.GetRequiredService<IUnitOfWorkManager>();
    protected BusinessAccountSubscriptionRequestBillPlanFactory BusinessAccountSubscriptionRequestBillPlanFactory => LazyServiceProvider.LazyGetRequiredService<BusinessAccountSubscriptionRequestBillPlanFactory>();

    public BusinessAccountSubscriptionRequestManager(IRepository<BusinessAccountSubscriptionRequest, Guid> businessAccountSubscriptionRequestRepository)
    {
        _businessAccountSubscriptionRequestRepository = businessAccountSubscriptionRequestRepository;
    }

    protected PromoCodeManager PromoCodeManager =>
        LazyServiceProvider.LazyGetRequiredService<PromoCodeManager>();

    public async Task<Guid> CreateAsync(Guid ownerId, string companyName, Address address, string accountName,
        TrackerInstallationLocation trackerInstallationLocation, List<SubscriptionVehicleInfo> subscriptionVehicleInfos,
        SubscriptionPlan subscriptionPlan, int userCount, int subscriptionDurationInMonths,string? promoCode,
        Guid? smsBundleId = null)
    {
        var hasBusinessRequest = await _businessAccountSubscriptionRequestRepository.AnyAsync(x => x.OwnerId == ownerId && x.Status != RequestStatus.Rejected && x.Status != RequestStatus.Canceled);
        if (hasBusinessRequest)
            throw new BusinessException(GoTrackDomainErrorCodes.UserAlreadyHasBusinessSubscription);

        if (!string.IsNullOrEmpty(promoCode))
        {
            await PromoCodeManager.ValidatePromoCodeAsync(promoCode);
        }

        var request = new BusinessAccountSubscriptionRequest(
            GuidGenerator.Create(),
            ownerId,
            companyName,
            address,
            accountName,
            trackerInstallationLocation,
            subscriptionVehicleInfos,
            subscriptionPlan,
            userCount,
            subscriptionDurationInMonths,
            promoCode,
            smsBundleId);

        var billId = await BillManager.CreateBillAsync(await GenerateBillPlan(request));
        request.SetBillId(billId);

        var minimumObserversCount = SubscriptionPlanConsts.MinimumObserversCount[subscriptionPlan.ToString()];

        if (userCount < minimumObserversCount)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.ObserverCountTooLow)
                .WithData("MinimumRequired", minimumObserversCount);
        }

        await _businessAccountSubscriptionRequestRepository.InsertAsync(request, true);


        return request.Id;
    }

    public async Task ApplyDiscountAsync(Guid id, decimal discountRate, string? note)
    {
        var request = await _businessAccountSubscriptionRequestRepository.GetAsync(id);

        if (note != null)
            request.AddNote(new RequestNote(GuidGenerator.Create(), request.Id, note));

        if (request.BillId is null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.BillNotAssigned);
        }

        var bill = await BillRepository.GetAsync(request.BillId.Value);
        await DiscountManager.ApplyDiscountOnSpecificRequest(request, bill, discountRate, true);
        bill.Approve();
        await BillRepository.UpdateAsync(bill, true);

        request.StartProcessing();
        request.SetStageAsPayment();

        await _businessAccountSubscriptionRequestRepository.UpdateAsync(request, true);
    }

    public async Task InstallDevicesAsync(Guid requestId, List<DeviceInstallationRequest> deviceRequests)
    {
        var request = await _businessAccountSubscriptionRequestRepository.GetAsync(requestId);

        var distinctDeviceRequests = deviceRequests
            .GroupBy(v => new { v.Serial, v.LicensePlateSubClass })
            .Select(g => g.First())
            .ToList();

        if (distinctDeviceRequests.Count != deviceRequests.Count)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DuplicateEntriesFoundForLicensePlateSerialAndSubClass);
        }

        if (distinctDeviceRequests.Count != request.TrackVehicles.Count)
        {
            throw new BusinessException(GoTrackDomainErrorCodes
                .TheNumberOfVehicleAndDeviceDoesNotMatchTheNumberOfRequest);
        }

        var businessTrackAccount = BusinessTrackAccountManager.Create(
            request.AccountName,
            request.CompanyName,
            request.CompanyAddress,
            request.OwnerId
        );

        await BusinessTrackAccountRepository.InsertAsync(businessTrackAccount, true);
        request.SetCreatedTrackAccount(businessTrackAccount.Id);
        request.SetStageAsDeviceInstallation();

        foreach (var deviceRequest in deviceRequests)
        {
            var device = await DeviceRepository.GetAsync(deviceRequest.DeviceId);

            var matchingVehicle = request.TrackVehicles.SingleOrDefault(subscriptionVehicle =>
                subscriptionVehicle.LicensePlate.Serial == deviceRequest.Serial &&
                subscriptionVehicle.LicensePlate.SubClass == deviceRequest.LicensePlateSubClass);

            if (matchingVehicle == null)
            {
                throw new BusinessException(GoTrackDomainErrorCodes.SomeLicensePlateDoNotMatchWithRequest);
            }

            if (matchingVehicle.NeedsTrackingDevice)
            {
                if (device.OwnerId is not null)
                    throw new BusinessException(GoTrackDomainErrorCodes.DeviceAlreadyOwned);
                if (await DeviceManager.CheckDeviceAvailabilityAsync(device.Id) is false)
                    throw new BusinessException(GoTrackDomainErrorCodes.TheDeviceStatusMustBeInoperative);
            }

            if (matchingVehicle.NeedsTrackingDevice is false)
            {
                if (device.OwnerId != request.OwnerId)
                    throw new BusinessException(GoTrackDomainErrorCodes.TrackingDeviceOwnerMismatch);
                if (device.Status != DeviceStatus.ConnectedAndDeactive)
                    throw new BusinessException(GoTrackDomainErrorCodes.DeviceStatusInvalidForNonTracking);
            }

            matchingVehicle.SetDeviceId(deviceRequest.DeviceId);

            if (matchingVehicle.NeedsTrackingDevice)
            {
                device.SetOwnerId(request.OwnerId);
                await DeviceManager.SetDeviceActiveAsync(device.Id);
                await DeviceRepository.UpdateAsync(device);
            }

            var vehicle = await VehicleManager.CreateAsync(
                businessTrackAccount.Id,
                new LicensePlate(matchingVehicle.LicensePlate.SubClass, matchingVehicle.LicensePlate.Serial),
                matchingVehicle.Color, matchingVehicle.ConsumptionRate
            );

            await VehicleRepository.InsertAsync(vehicle);

            await DeviceEventLogManager.CreateAsync(
                vehicle.Id,
                matchingVehicle.DeviceId ?? throw new EntityNotFoundException(typeof(Device), matchingVehicle.DeviceId),
                EventName.Installed
            );
        }

        await _businessAccountSubscriptionRequestRepository.UpdateAsync(request);
    }


    public async Task FinishProcessingAsync(Guid id, string? note)
    {
        var subscriptionRequest = await _businessAccountSubscriptionRequestRepository.GetAsync(id);

        if (!string.IsNullOrEmpty(note))
        {
            subscriptionRequest.AddNote(
                new RequestNote(
                    GuidGenerator.Create(),
                    subscriptionRequest.Id,
                    note
                )
            );
        }


        var smsBundleMessagesCount = subscriptionRequest.SmsBundle?.MessagesCount ?? 0;
        var subscriptionRequestMessageCount = smsBundleMessagesCount * subscriptionRequest.SubscriptionDurationInMonths;

        await TrackAccountSubscriptionRepository.InsertAsync(
            new TrackAccountSubscription(
                GuidGenerator.Create(),
                subscriptionRequest.CreatedTrackAccountId ?? throw new Exception(),
                subscriptionRequest.SubscriptionPlan,
                subscriptionRequest.UserCount,
                subscriptionRequestMessageCount,
                subscriptionRequest.SubscriptionDurationInMonths,
                TrackAccountSubscriptionState.Active,
                Clock
            ),
            true
        );

        await TrackAccountSubscriptionManager.AddFeatureBasedOnPlanAsync(
            subscriptionRequest.CreatedTrackAccountId!.Value,
            subscriptionRequest.SubscriptionPlan
        );


        subscriptionRequest.FinishProcessing();
        subscriptionRequest.SetStageAsFinish();

        await _businessAccountSubscriptionRequestRepository.UpdateAsync(subscriptionRequest);
    }

    public async Task HandlePaymentCompletionAsync(Guid requestId)
    {
        var request = await _businessAccountSubscriptionRequestRepository.GetAsync(requestId);

        request.SetStageAsPaymentReview();
    }

    public async Task<BillPlan> GenerateBillPlan(BusinessAccountSubscriptionRequest request)
    {
        return await BusinessAccountSubscriptionRequestBillPlanFactory.GenerateBillPlan
        (
            new BusinessAccountSubscriptionRequestBillPlanInput
            (
                request.Id,
                request.OwnerId,
                request.SubscriptionPlan,
                request.SubscriptionDurationInMonths,
                request.UserCount,
                request.TrackVehicles.Count(x => x.NeedsTrackingDevice),
                request.TrackVehicles.Count,
                request.SmsBundleId,
                request.PromoCode
            )
        );
    }
}