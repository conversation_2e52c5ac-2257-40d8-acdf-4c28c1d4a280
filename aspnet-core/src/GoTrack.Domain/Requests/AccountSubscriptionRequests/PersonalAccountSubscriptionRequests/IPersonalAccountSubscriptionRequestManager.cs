using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GoTrack.SubscriptionPlans;

namespace GoTrack.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests;

public interface IPersonalAccountSubscriptionRequestManager : IBillableRequestManager<PersonalAccountSubscriptionRequest>
{
    Task<Guid> CreateAsync(Guid ownerId, string accountName,
        TrackerInstallationLocation trackerInstallationLocation, List<SubscriptionVehicleInfo> subscriptionVehicleInfos,
        SubscriptionPlan subscriptionPlan, int userCount, int subscriptionDurationInMonths, string? promoCode,
        Guid? smsBundleId = null);
    PersonalAccountSubscriptionRequest CreateTempAsync(Guid ownerId, string accountName,
        TrackerInstallationLocation trackerInstallationLocation, List<SubscriptionVehicleInfo> subscriptionVehicleInfos,
        SubscriptionPlan subscriptionPlan, int userCount, int subscriptionDurationInMonths,
        Guid? smsBundleId = null);
    Task ApplyDiscountAsync(Guid id, decimal discountRate, string? note);
    Task InstallDevicesAsync(Guid requestId, List<DeviceInstallationRequest> deviceRequests);
    Task FinishProcessingAsync(Guid id, string? note);
}