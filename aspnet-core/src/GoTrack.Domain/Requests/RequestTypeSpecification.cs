using System;
using System.Linq.Expressions;
using Volo.Abp.Specifications;

namespace GoTrack.Requests;

public class RequestTypeSpecification : Specification<Request>
{
    private readonly RequestType _type;
    public RequestTypeSpecification(RequestType type)
    {
        _type = type;
    }

    public override Expression<Func<Request, bool>> ToExpression()
    {
        return r => r.Type == _type;
    }
}