using System;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace GoTrack.VehicleDeviceEventLogs;

public class VehicleDeviceEventLogManager : DomainService, IVehicleDeviceEventLogManager
{
    private readonly IRepository<VehicleDeviceEventLog, Guid> _vehicleDeviceEventLogRepository;

    public VehicleDeviceEventLogManager(IRepository<VehicleDeviceEventLog, Guid> vehicleDeviceEventLogRepository)
    {
        _vehicleDeviceEventLogRepository = vehicleDeviceEventLogRepository;
    }

    public async Task CreateAsync(Guid vehicleId, Guid deviceId, EventName eventName)
    {
        var lastVehicleDeviceEventLog = (await _vehicleDeviceEventLogRepository.GetQueryableAsync())
            .OrderByDescending(e => e.CreationTime)
            .FirstOrDefault(log => log.VehicleId == vehicleId && log.DeviceId == deviceId);

        if (lastVehicleDeviceEventLog is not null)
        {
            if (eventName == EventName.Installed && lastVehicleDeviceEventLog.EventName == EventName.Installed)
            { 
                throw new BusinessException(GoTrackDomainErrorCodes.DeviceIsAlreadyInstalled); 
            }

            if (eventName == EventName.Uninstalled && lastVehicleDeviceEventLog.EventName == EventName.Uninstalled)
            {
                throw new BusinessException(GoTrackDomainErrorCodes.DeviceIsAlreadyUninstalled);
            }
        }

        if (eventName == EventName.Uninstalled)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.InitialStatusOfAssigningDeviceToVehicleMustBeInstalled);

        }

        await _vehicleDeviceEventLogRepository.InsertAsync(new VehicleDeviceEventLog(vehicleId, deviceId, eventName));
    }

}