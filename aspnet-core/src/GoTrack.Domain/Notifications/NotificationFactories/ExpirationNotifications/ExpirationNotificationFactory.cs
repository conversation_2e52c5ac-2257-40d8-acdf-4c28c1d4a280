using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using GoTrack.Localization;
using Microsoft.Extensions.Localization;
using Notify;
using Notify.Provider.Sms;
using Volo.Abp.DependencyInjection;

namespace GoTrack.Notifications.NotificationFactories.ExpirationNotifications;

public class ExpirationNotificationFactory : NotificationFactory<ExpirationNotificationDataModel, CreateSmsNotificationEto>, ITransientDependency
{
    private readonly IStringLocalizer<GoTrackResource> _localizer;

    public ExpirationNotificationFactory(IStringLocalizer<GoTrackResource> localizer)
    {
        _localizer = localizer;
    }

    public override async Task<CreateSmsNotificationEto> CreateAsync(ExpirationNotificationDataModel model, IEnumerable<Guid> userIds)
    {
        var text = _localizer["ExpirationNotification", model.DaysUntilExpiration];
        return new CreateSmsNotificationEto(userIds, text);
    }
}