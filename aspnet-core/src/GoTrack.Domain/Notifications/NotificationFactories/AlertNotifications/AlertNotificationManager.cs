using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.AlertDefinitions;
using GoTrack.Alerts.AlertTriggers;
using GoTrack.Localization;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using GoTrack.TrackAccounts.TrackAccountSubscriptions.Settings;
using GoTrack.UserTrackAccountAssociations;
using Microsoft.Extensions.Localization;
using Notify.Provider.Email;
using Notify.Provider.FCM;
using Notify.Provider.Sms;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Settings;

namespace GoTrack.Notifications.NotificationFactories.AlertNotifications;

public class AlertNotificationManager : DomainService
{
    private IUserTrackAccountAssociationManager UserTrackAccountAssociationManager
        => LazyServiceProvider.LazyGetRequiredService<IUserTrackAccountAssociationManager>();

    private IAlertTriggerManagerResolver AlertTriggerManagerResolver
        => LazyServiceProvider.LazyGetRequiredService<IAlertTriggerManagerResolver>();

    private TrackAccountSubscriptionManager TrackAccountSubscriptionManager
        => LazyServiceProvider.LazyGetRequiredService<TrackAccountSubscriptionManager>();

    private IRepository<UserTrackAccountAssociation, Guid> UserTrackAccountAssociationRepository
        => LazyServiceProvider.LazyGetRequiredService<IRepository<UserTrackAccountAssociation, Guid>>();

    private TrackAccountUserNotificationManager TrackAccountUserNotificationManager
        => LazyServiceProvider.LazyGetRequiredService<TrackAccountUserNotificationManager>();

    private ISettingProvider SettingProvider
        => LazyServiceProvider.LazyGetRequiredService<ISettingProvider>();

    private readonly IStringLocalizer<GoTrackResource> _localizer;
    private readonly IDistributedEventBus _distributedEventBus;

    public AlertNotificationManager(IStringLocalizer<GoTrackResource> localizer, IDistributedEventBus distributedEventBus)
    {
        _localizer = localizer;
        _distributedEventBus = distributedEventBus;
    }

    public async Task SendNotificationAsync(AlertTrigger alertTrigger, DateTime notificationDateTime)
    {
        var userIdsForNotification = await GetUserIdsForNotificationAsync(alertTrigger);
        var alertTriggerManager = AlertTriggerManagerResolver.GetAlertTriggerManager(alertTrigger.Type);
        var message = await alertTriggerManager.CreateNotificationMessageAsync(alertTrigger.Id, notificationDateTime);

        if (alertTrigger.NotificationMethods.Contains(AlertDefinitionNotificationMethod.MobileNotification))
        {
            await SendMobileNotificationAsync(userIdsForNotification, message, alertTrigger);
        }

        if (alertTrigger.NotificationMethods.Contains(AlertDefinitionNotificationMethod.Mailing))
        {
            await SendEmailNotificationAsync(userIdsForNotification, message);
        }

        if (alertTrigger.NotificationMethods.Contains(AlertDefinitionNotificationMethod.Sms))
        {
            await SendSmsNotificationAsync(alertTrigger, userIdsForNotification, message);
        }
    }

    private async Task SendMobileNotificationAsync(List<Guid> userIds, string message, AlertTrigger alertTrigger)
    {
        var title = _localizer["GoTrack:VehicleAlert"];
        var fcmEto = new CreateFCMNotificationEto(title, message, userIds);

        await TrackAccountUserNotificationManager.CreateUserNotificationWithTrackAccountIdAsync(
            fcmEto,
            alertTrigger.TrackAccountId
        );

        await _distributedEventBus.PublishAsync(fcmEto);
    }

    private async Task SendEmailNotificationAsync(List<Guid> userIds, string message)
    {
        var subject = _localizer["GoTrack:VehicleAlert"];
        var emailEto = new CreateEmailNotificationEto(userIds, subject, message);
        await _distributedEventBus.PublishAsync(emailEto);
    }

     private async Task SendSmsNotificationAsync(AlertTrigger alertTrigger, List<Guid> userIdsForNotification, string message)
     {
         var trackAccountSubscription = await TrackAccountSubscriptionManager
             .GetCurrentActiveTrackAccountSubscriptionAsync(alertTrigger.TrackAccountId);

         if (trackAccountSubscription.SmsBundleCount <= 0)
         {
             // No bundle available, skip sending SMS
             return;
         }

         var numberOfSmsToSend = userIdsForNotification.Count;
         var availableSms = trackAccountSubscription.SmsBundleCount;
         var userIdsForSend = userIdsForNotification.ToList();
         var smsExpiredJustNow = false;
         var smsLowCount = false;

         if (numberOfSmsToSend >= availableSms)
         {
             numberOfSmsToSend = availableSms;
             userIdsForSend = userIdsForSend.Take(numberOfSmsToSend).ToList();
             smsExpiredJustNow = true;
         }
         
         var smsLowCountThreshold = await SettingProvider.GetAsync(
             GoTrackSettingKeys.SmsLowCountThreshold,
             GoTrackSettingKeys.SmsLowCountThresholdDefaultValue);

         if (trackAccountSubscription.SmsBundleCount >= smsLowCountThreshold &&
             trackAccountSubscription.SmsBundleCount - numberOfSmsToSend <= smsLowCountThreshold &&
             trackAccountSubscription.SmsBundleCount - numberOfSmsToSend > 0 )
         {
             smsLowCount = true;
         }

         var smsEto = new CreateSmsNotificationEto(userIdsForSend, message);
         await _distributedEventBus.PublishAsync(smsEto);

         await TrackAccountSubscriptionManager.SetSmsBundleCountAsync(trackAccountSubscription.Id, trackAccountSubscription.SmsBundleCount - numberOfSmsToSend);

         if (smsExpiredJustNow)
         {
             await SendSmsBundleExpiredNotificationAsync(alertTrigger.TrackAccountId, userIdsForNotification);
         }
         else if (smsLowCount)
         {
             await SendSmsBundleLowNotificationAsync(alertTrigger.TrackAccountId, userIdsForNotification, availableSms);
         }
     }

     private async Task SendSmsBundleExpiredNotificationAsync(Guid alertTriggerTrackAccountId, List<Guid> userIdsForNotification)
    {
        var owner = await UserTrackAccountAssociationRepository.GetAsync(x =>
            x.UserId != null && x.AssociationType == AssociationType.Owner &&
            x.TrackAccountId == alertTriggerTrackAccountId);

        var expiryMessage = _localizer["SMSBundleExpiredMessage"];
        var expirySmsEto = new CreateSmsNotificationEto(owner.UserId!.Value, expiryMessage);
        await _distributedEventBus.PublishAsync(expirySmsEto);

        var title = _localizer["SMSBundleExpiredTitle"];
        var fcmEto = new CreateFCMNotificationEto(title, expiryMessage, userIdsForNotification);
        await _distributedEventBus.PublishAsync(fcmEto);
    }

    private async Task SendSmsBundleLowNotificationAsync(Guid alertTriggerTrackAccountId, List<Guid> userIdsForNotification, int remainingSmsCount)
    {
        var owner = await UserTrackAccountAssociationRepository.GetAsync(x =>
            x.UserId != null && x.AssociationType == AssociationType.Owner &&
            x.TrackAccountId == alertTriggerTrackAccountId);

        var lowCountMessage = _localizer["SMSBundleLowCountMessage", remainingSmsCount];
        var lowCountSmsEto = new CreateSmsNotificationEto(owner.UserId!.Value, lowCountMessage);
        await _distributedEventBus.PublishAsync(lowCountSmsEto);

        var title = _localizer["SMSBundleLowCountTitle"];
        var fcmEto = new CreateFCMNotificationEto(title, lowCountMessage, userIdsForNotification);
        await _distributedEventBus.PublishAsync(fcmEto);
    }

    private async Task<List<Guid>> GetUserIdsForNotificationAsync(AlertTrigger alertTrigger)
    {
        var userTrackAccountAssociationsQuery =
            await UserTrackAccountAssociationManager
                .GetQueryableOfUsersThatRelatesToVehicleAsync(alertTrigger.VehicleId);

        var userIdsQuery = userTrackAccountAssociationsQuery
            .Where(x => x.UserId != null)
            .Select(x => x.UserId)
            .Distinct()
            .Cast<Guid>();

        return await AsyncExecuter.ToListAsync(userIdsQuery);
    }
}