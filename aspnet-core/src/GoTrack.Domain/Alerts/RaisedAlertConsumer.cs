using GoTrack.Alerts.AlertTriggers;
using GoTrack.Alerts.Contracts.Exchanges.RaisedAlert;
using GoTrack.Alerts.Notifications;
using MassTransit;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using Volo.Abp.Domain.Services;

namespace GoTrack.Alerts;

public class RaisedAlertConsumer : DomainService, IConsumer<RaisedAlert>
{
    private readonly RaisedAlertNotificationManager _raisedAlertNotificationManager;

    public RaisedAlertConsumer(RaisedAlertNotificationManager raisedAlertNotificationManager)
    {
        _raisedAlertNotificationManager = raisedAlertNotificationManager;
    }

    public Task Consume(ConsumeContext<RaisedAlert> context)
    {
        try
        {
            Logger.LogInformation(
                "Receive new Message: Message={Message}, " +
                "DeviceId={DeviceId}, " +
                "AlertTriggerId={AlertTriggerId}, " +
                "StartTimestamp={StartTimestamp}, " +
                "EndTimestamp={EndTimestamp}, " +
                context.Message,
                context.Message.AlertTriggerId,
                context.Message.StartTimestamp,
                context.Message.EndTimestamp
            );

            return _raisedAlertNotificationManager.HandleRaisedAlertAsync(context.Message);
        }
        catch (Exception ex) 
        {
            Logger.LogError(message: ex.Message);
            Logger.LogError(message: ex.StackTrace);
            Logger.LogError(message: ex.ToString());
            Logger.LogError(message: ex.InnerException?.Message);
            Logger.LogError(message: ex.InnerException?.StackTrace);
            Logger.LogError(message: ex.InnerException?.ToString());

            throw;
        }
    }
}