using GoTrack.AlertDefinitions;
using GoTrack.AlertDefinitions.AlertDefinitionAssociations;
using GoTrack.AlertDefinitions.RouteAlertDefinitions.ExitingRouteAlertDefinitions;
using GoTrack.Alerts.Contracts.Exchanges.Enums;
using GoTrack.Alerts.Contracts.Exchanges.IAlertCrud;
using GoTrack.Routes;
using GoTrack.VehicleDeviceEventLogs;
using GoTrack.Vehicles;
using MassTransit;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace GoTrack.Alerts.AlertTriggers.RouteAlertTriggers.ExitingRouteAlertTriggers;

public class ExitingRouteAlertTriggerManager : AlertTriggerManager
{
    private readonly IRepository<ExitingRouteAlertTrigger, Guid> _exitingRouteAlertTriggerRepository;
    private readonly IRepository<Route, Guid> _routeRepository;

    public ExitingRouteAlertTriggerManager(
        IRepository<AlertTrigger, Guid> alertTriggerRepository,
        IRepository<AlertDefinitionAssociation, Guid> alertDefinitionAssociationRepository,
        IRepository<VehicleDeviceEventLog, Guid> vehicleDeviceEventLogRepository,
        IRepository<AlertDefinition, Guid> alertDefinitionRepository,
        IPublishEndpoint publishEndpoint,
        IStringLocalizerFactory stringLocalizerFactory,
        IRepository<ExitingRouteAlertTrigger, Guid> exitingRouteAlertTriggerRepository,
        IRepository<Route, Guid> routeRepository)
        : base(alertTriggerRepository,
            alertDefinitionAssociationRepository,
            vehicleDeviceEventLogRepository,
            alertDefinitionRepository,
            publishEndpoint, stringLocalizerFactory
        )
    {
        _exitingRouteAlertTriggerRepository = exitingRouteAlertTriggerRepository;
        _routeRepository = routeRepository;
    }

    public override async Task<LocalizedString> CreateNotificationMessageAsync(Guid alertTriggerId, DateTime notificationDateTime)
    {
        var query = await _exitingRouteAlertTriggerRepository.WithDetailsAsync(x => x.Vehicle);

        var exitingRouteAlertTrigger = await AsyncExecuter.SingleAsync(query.Where(x => x.Id == alertTriggerId));

        var route = await _routeRepository.GetAsync(exitingRouteAlertTrigger.RouteId);

        var message = Localizer[
            "GoTrack:ExitingRouteMessage",
            exitingRouteAlertTrigger.Vehicle.LicensePlate.Serial,
            route.Name,
            notificationDateTime
        ];

        return message;
    }


    public override List<AlertTrigger> GenerateAlertTriggers(AlertDefinition alertDefinition, ICollection<Vehicle> uniqueVehicles)
    {
        var exitingRouteAlertDefinition = alertDefinition as ExitingRouteAlertDefinition;

        return uniqueVehicles
            .SelectMany(vehicle => exitingRouteAlertDefinition!.RouteAlertRoutes
                .Select(routeAlertRoute =>
                    new ExitingRouteAlertTrigger(
                        GuidGenerator.Create(),
                        [.. exitingRouteAlertDefinition.NotificationMethods],
                        exitingRouteAlertDefinition.TrackAccountId,
                        exitingRouteAlertDefinition.Id,
                        vehicle.Id,
                        routeAlertRoute.RouteId
                    )
                )
            ).ToList<AlertTrigger>();
    }

    public override async Task<AlertCrud> GenerateAlertCrudAsync(AlertTrigger trigger, CrudType crudType)
    {
        var exitingRouteAlertTrigger = trigger as ExitingRouteAlertTrigger;

        var lastVehicleDeviceEventLog = (await VehicleDeviceEventLogRepository.WithDetailsAsync(x => x.Device))
                .OrderByDescending(e => e.CreationTime)
                .FirstOrDefault(log => log.VehicleId == exitingRouteAlertTrigger!.VehicleId && log.EventName == EventName.Installed);

        var route = await _routeRepository.GetAsync(exitingRouteAlertTrigger!.RouteId);
        return new ZoneInOutAlertCrud()
        {
            Id = exitingRouteAlertTrigger.Id,
            Code = AlertCode.ZoneOut,
            AffectiveFrom = Clock.Now,
            CrudType = crudType,
            Imei = lastVehicleDeviceEventLog?.Device.Imei,
            Polygon = route.HhCode,
        };
    }
}
