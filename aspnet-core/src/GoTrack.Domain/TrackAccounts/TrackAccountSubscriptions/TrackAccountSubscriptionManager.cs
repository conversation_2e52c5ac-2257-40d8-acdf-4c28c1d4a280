using GoTrack.SubscriptionPlans;
using System.Threading.Tasks;
using System;
using System.Collections.Generic;
using System.Linq;
using GoTrack.Devices;
using GoTrack.Notifications.NotificationFactories.ExpirationNotifications;
using GoTrack.TrackAccounts.TrackAccountSubscriptions.Settings;
using GoTrack.Requests.RenewTrackAccountSubscriptions;
using Volo.Abp.Domain.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.FeatureManagement;
using GoTrack.TrackAccounts.TrackAccountSubscriptions.SubscriptionFeatures;
using GoTrack.UserTrackAccountAssociations;
using GoTrack.VehicleDeviceEventLogs;
using GoTrack.Vehicles;
using GoTrack.Vehicles.LicensePlates;
using Volo.Abp;
using Microsoft.Extensions.Logging;
using Volo.Abp.EventBus.Distributed;
using Volo.Abp.Settings;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Data;

namespace GoTrack.TrackAccounts.TrackAccountSubscriptions;

public class TrackAccountSubscriptionManager : DomainService
{
    private readonly IFeatureManager _featureManager;
    private readonly ITrackAccountRepository _trackAccountRepository;
    private readonly IRepository<TrackAccountSubscription, Guid> _trackAccountSubscriptionRepository;
    private readonly IRepository<Vehicle, Guid> _vehicleRepository;
    private readonly UserTrackAccountAssociationManager _userTrackAccountAssociationManager;
    private readonly IRepository<UserTrackAccountAssociation, Guid> _userTrackAccountAssociationRepository;
    private readonly IRepository<RenewSubscriptionRequest, Guid> _renewSubscriptionRequestRepository;
    private readonly IVehicleManager _vehicleManager;
    private readonly IVehicleDeviceEventLogManager _deviceEventLogManager;
    private readonly IDeviceManager _deviceManager;
    private readonly ISettingProvider _settingProvider;
    private readonly IDistributedEventBus _distributedEventBus;
    private readonly IDataFilter<IHaveTrackAccount> _dataFilter;

    protected IVehicleManager VehicleManager =>
        LazyServiceProvider.GetRequiredService<IVehicleManager>();

    public TrackAccountSubscriptionManager(
        IFeatureManager featureManager,
        ITrackAccountRepository trackAccountRepository,
        IRepository<TrackAccountSubscription, Guid> trackAccountSubscriptionRepository,
        IRepository<Vehicle, Guid> vehicleRepository,
        UserTrackAccountAssociationManager userTrackAccountAssociationManager,
        IRepository<UserTrackAccountAssociation, Guid> userTrackAccountAssociationRepository,
        IRepository<RenewSubscriptionRequest, Guid> renewSubscriptionRequestRepository,
        IVehicleManager vehicleManager,
        IVehicleDeviceEventLogManager deviceEventLogManager,
        IDeviceManager deviceManager,
        ISettingProvider settingProvider,
        IDistributedEventBus distributedEventBus,
        IDataFilter<IHaveTrackAccount> dataFilter)
    {
        _featureManager = featureManager;
        _trackAccountRepository = trackAccountRepository;
        _trackAccountSubscriptionRepository = trackAccountSubscriptionRepository;
        _vehicleRepository = vehicleRepository;
        _userTrackAccountAssociationManager = userTrackAccountAssociationManager;
        _renewSubscriptionRequestRepository = renewSubscriptionRequestRepository;
        _vehicleManager = vehicleManager;
        _deviceEventLogManager = deviceEventLogManager;
        _deviceManager = deviceManager;
        _settingProvider = settingProvider;
        _distributedEventBus = distributedEventBus;
        _dataFilter = dataFilter;
        this._userTrackAccountAssociationRepository = userTrackAccountAssociationRepository;
    }
    
    public Task<TrackAccountSubscription> GetCurrentActiveTrackAccountSubscriptionAsync(Guid trackAccountId)
    {
        return _trackAccountSubscriptionRepository.GetAsync(x =>
            x.TrackAccountId == trackAccountId &&
            x.State == TrackAccountSubscriptionState.Active
        );
    }
    public async Task<int> GetRemainingMonths(Guid trackAccountSubscriptionId)
    {
        var subscription = await
            _trackAccountSubscriptionRepository.GetAsync(x => x.Id == trackAccountSubscriptionId);

        var remainingMonths = (subscription.To - Clock.Now).TotalDays / 30;

        return (int)Math.Ceiling(remainingMonths);
    }

    public async Task AddFeatureBasedOnPlanAsync(Guid trackAccountId, SubscriptionPlan subscriptionPlan)
    {
        switch (subscriptionPlan)
        {
            case SubscriptionPlan.Basic:
                await AddFeatureToBasicPlanAsync(trackAccountId);
                break;
            case SubscriptionPlan.Standard:
                await AddFeatureToStandardPlanAsync(trackAccountId);
                break;
            case SubscriptionPlan.Premium:
                await AddFeatureToPremiumPlanAsync(trackAccountId);
                break;
        }
    }


    public async Task ActivateSubscriptionAsync(Guid trackAccountSubscriptionId)
    {
        var subscription = await _trackAccountSubscriptionRepository.GetAsync(trackAccountSubscriptionId);
        var renewSubscriptionRequest = await _renewSubscriptionRequestRepository.GetAsync(request =>
            request.TrackAccountSubscriptionId == trackAccountSubscriptionId);

        if (subscription.State == TrackAccountSubscriptionState.Active)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.SubscriptionAlreadyActive);
        }

        if (renewSubscriptionRequest.RemoveTrackVehicles.Any())
        {
            await _vehicleRepository.DeleteManyAsync(renewSubscriptionRequest.RemoveTrackVehicles);
        }

        var userAssociations = await _userTrackAccountAssociationRepository.GetQueryableAsync();
        foreach (var removeUser in renewSubscriptionRequest.RemoveUsers)
        {
            var userTrackAssociation = userAssociations
                .SingleOrDefault(association => association.TrackAccountId == removeUser);

            if (userTrackAssociation != null &&
                userTrackAssociation.Status != UserTrackAccountAssociationStatus.Deactive)
            {
                await _userTrackAccountAssociationManager.DeactivateUserAsync(userTrackAssociation.UserId.Value,
                    userTrackAssociation.TrackAccountId);
            }
        }

        foreach (var newVehicle in renewSubscriptionRequest.NewTrackVehicles)
        {
            var vehicle = await _vehicleManager.CreateAsync(
                subscription.TrackAccountId,
                new LicensePlate(newVehicle.LicensePlate.SubClass, newVehicle.LicensePlate.Serial),
                newVehicle.Color,
                newVehicle.ConsumptionRate
            );

            await _vehicleRepository.InsertAsync(vehicle);

            if (newVehicle.DeviceId is null)
            {
                throw new BusinessException(GoTrackDomainErrorCodes.DeviceRequiredForActivation);
            }

            await _deviceManager.SetDeviceActiveAsync(newVehicle.DeviceId.Value);

            await _deviceEventLogManager.CreateAsync(vehicle.Id, newVehicle.DeviceId.Value, EventName.Installed);
        }

        subscription.SetActivateState();
        await _trackAccountSubscriptionRepository.UpdateAsync(subscription);
    }

    public async Task ExpireSubscriptionsAsync()
    {
        using var _ = _dataFilter.Disable();

        await ExpireSubscriptionsWithPendingRenewalsAsync();

        await ExpireSubscriptionsPastGracePeriodAsync();
    }

    public async Task NotifyExpiringSubscriptionsAsync()
    {
        var notificationDays = await GetNotificationDaysAsync();
        var today = Clock.Now.Date;

        await NotifyExpiringSubscriptionsAsync(today, notificationDays);
    }

    public async Task SetSmsBundleCountAsync(Guid trackAccountSubscriptionId, int newCount)
    {
        var trackAccountSubscription = await _trackAccountSubscriptionRepository.GetAsync(trackAccountSubscriptionId);

        trackAccountSubscription.SetSmsBundleCount(newCount);

        await _trackAccountSubscriptionRepository.UpdateAsync(trackAccountSubscription);
    }

    private async Task AddFeatureToBasicPlanAsync(Guid trackAccountId)
    {
        var trackAccount = await _trackAccountRepository.GetAsync(trackAccountId);
        var currentTrackAccountSubscription = await _trackAccountSubscriptionRepository.GetAsync(x =>
            x.TrackAccountId == trackAccount.Id &&
            x.State == TrackAccountSubscriptionState.Active
        );

        foreach (var feature in SubscriptionPlanConsts.BasicFeatures)
        {
            await _featureManager.SetForSubscriptionAsync(
                currentTrackAccountSubscription.Id,
                feature,
                true.ToString()
            );
        }
    }

    private async Task AddFeatureToStandardPlanAsync(Guid trackAccountId)
    {
        var trackAccount = await _trackAccountRepository.GetAsync(trackAccountId);
        var currentTrackAccountSubscription = await _trackAccountSubscriptionRepository.GetAsync(x =>
            x.TrackAccountId == trackAccount.Id &&
            x.State == TrackAccountSubscriptionState.Active
        );

        foreach (var feature in SubscriptionPlanConsts.StandardFeatures)
        {
            await _featureManager.SetForSubscriptionAsync(
                currentTrackAccountSubscription.Id,
                feature,
                true.ToString()
            );
        }
    }

    private async Task AddFeatureToPremiumPlanAsync(Guid trackAccountId)
    {
        var trackAccount = await _trackAccountRepository.GetAsync(trackAccountId);
        var currentTrackAccountSubscription = await _trackAccountSubscriptionRepository.GetAsync(x =>
            x.TrackAccountId == trackAccount.Id &&
            x.State == TrackAccountSubscriptionState.Active
        );

        foreach (var feature in SubscriptionPlanConsts.PremiumFeatures)
        {
            await _featureManager.SetForSubscriptionAsync(
                currentTrackAccountSubscription.Id,
                feature,
                true.ToString()
            );
        }
    }

    private async Task ExpireSubscriptionsWithPendingRenewalsAsync()
    {
        var now = Clock.Now.Date;

        var expiredSubscriptionsQuery = (await _trackAccountSubscriptionRepository.GetQueryableAsync())
            .Where(s => s.State != TrackAccountSubscriptionState.Expired && s.To < now);

        var pendingSubscriptionsQuery = (await _trackAccountSubscriptionRepository.GetQueryableAsync())
            .Where(s => s.State == TrackAccountSubscriptionState.Pending);

        var accountsWithPending = await AsyncExecuter.ToListAsync(
            pendingSubscriptionsQuery.Select(s => s.TrackAccountId).Distinct()
        );

        if (!accountsWithPending.Any())
        {
            return;
        }

        var subscriptionsToExpireQuery = expiredSubscriptionsQuery.Where(s =>
            accountsWithPending.Contains(s.TrackAccountId));

        var subscriptionsToExpire = await AsyncExecuter.ToListAsync(
            subscriptionsToExpireQuery
        );

        foreach (var subscription in subscriptionsToExpire)
        {
            await TerminateSubscriptionWithPendingRenewalAsync(subscription);
        }
    }



    private async Task TerminateSubscriptionWithPendingRenewalAsync(TrackAccountSubscription subscription)
    {
        var pendingSubscription = await _trackAccountSubscriptionRepository.SingleAsync(x =>
            x.TrackAccountId == subscription.TrackAccountId && x.State == TrackAccountSubscriptionState.Pending);
        subscription.SetExpired();
        pendingSubscription.SetActive(); //TODO
        await _trackAccountSubscriptionRepository.UpdateManyAsync([subscription, pendingSubscription], true);
    }

    private async Task ExpireSubscriptionsPastGracePeriodAsync()
    {
        var gracePeriod = await _settingProvider.GetAsync(
            GoTrackSettingKeys.GracePeriod,
            GoTrackSettingKeys.GracePeriodDefaultValue);

        if (gracePeriod < 0)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.GracePeriodMustBeNonNegative);
        }

        var now = Clock.Now.Date;
        var thresholdDate = now.AddDays(-gracePeriod);

        var subscriptionsQuery = (await _trackAccountSubscriptionRepository.GetQueryableAsync())
            .Where(s => s.State != TrackAccountSubscriptionState.Expired && s.To <= thresholdDate);


        var expiredSubscriptions = await AsyncExecuter.ToListAsync(
            subscriptionsQuery
        );

        foreach (var subscription in expiredSubscriptions)
        {
            await TerminateSubscriptionAsync(subscription);
        }
    }

    private async Task TerminateSubscriptionAsync(TrackAccountSubscription subscription)
    {
        await VehicleManager.SetVehiclesDevicesDeactiveAsync(subscription.TrackAccountId);
        subscription.SetExpired();
        await _trackAccountSubscriptionRepository.UpdateAsync(subscription);
    }


    private async Task<List<int>> GetNotificationDaysAsync()
    {
        var notificationDaysSetting =
            await _settingProvider.GetOrNullAsync(GoTrackSettingKeys.NotificationDays);

        var days = string.IsNullOrEmpty(notificationDaysSetting)
            ? GoTrackSettingKeys.NotificationDaysDefaultValue
            : notificationDaysSetting
                .Split(',')
                .Select(int.Parse)
                .OrderBy(x => x)
                .ToList();

        if (days.Any(x => x <= 0))
        {
            throw new BusinessException(GoTrackDomainErrorCodes.NotificationDaysMustBePositive);
        }

        return days;
    }

    private async Task NotifyExpiringSubscriptionsAsync(DateTime referenceDate, List<int> notificationDays)
    {
        foreach (var day in notificationDays)
        {
            await NotifyExpiringSubscriptionsForDayAsync(referenceDate, day);
        }
    }

    private async Task NotifyExpiringSubscriptionsForDayAsync(DateTime referenceDate, int notificationDay)
    {
        var subscriptionsQuery = await GetExpiringSubscriptionsQueryAsync(referenceDate, notificationDay);
        await ProcessSubscriptionsBatchedAsync(subscriptionsQuery, referenceDate);
    }

    public async Task<IQueryable<TrackAccountSubscription>> GetExpiringSubscriptionsQueryAsync(
        DateTime referenceDate, int notificationDay)
    {
        var expirationThreshold = referenceDate.Date.AddDays(notificationDay);
        var query = await _trackAccountSubscriptionRepository.GetQueryableAsync();

        return query.Where(s =>
                s.State == TrackAccountSubscriptionState.Active &&
                s.To <= expirationThreshold &&
                (!s.LastNotificationAt.HasValue ||
                 s.To.AddDays(-notificationDay) > s.LastNotificationAt.Value)
            )
            .OrderByDescending(s => s.To);
    }

    private async Task ProcessSubscriptionsBatchedAsync(IQueryable<TrackAccountSubscription> subscriptionsQuery,
        DateTime referenceDate)
    {
        using var _ = _dataFilter.Disable();

        var subscriptionsToNotify = await AsyncExecuter.ToListAsync(subscriptionsQuery);

        foreach (var subscription in subscriptionsToNotify)
        {
            if (!await SendExpirationNotificationAsync(subscription, referenceDate))
            {
                continue;
            }

            subscription.UpdateLastNotificationAt(referenceDate);
            await _trackAccountSubscriptionRepository.UpdateAsync(subscription);
        }
    }

    private async Task<bool> SendExpirationNotificationAsync(TrackAccountSubscription subscription,
        DateTime referenceDate)
    {
        var userTrackAccountAssociationQueryable = await _userTrackAccountAssociationRepository.GetQueryableAsync();
        var userId = await AsyncExecuter.SingleAsync(
            userTrackAccountAssociationQueryable
                .Where(x =>
                    x.TrackAccountId == subscription.TrackAccountId
                    && x.AssociationType == AssociationType.Owner
                    && x.UserId.HasValue)
                .Select(x => x.UserId!.Value));

        var expirationNotificationFactory =
            LazyServiceProvider.LazyGetRequiredService<ExpirationNotificationFactory>();

        try
        {
            var eto = await expirationNotificationFactory.CreateAsync(
                new ExpirationNotificationDataModel()
                {
                    DaysUntilExpiration = (subscription.To - referenceDate).Days
                },
                userId);
            await _distributedEventBus.PublishAsync(eto);
        }
        catch (Exception e)
        {
            Logger.LogError(e.Message);

            return false;
        }

        return true;
    }
}