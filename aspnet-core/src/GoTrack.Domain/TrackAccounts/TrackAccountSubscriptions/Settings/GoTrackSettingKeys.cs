using System.Collections.Generic;

namespace GoTrack.TrackAccounts.TrackAccountSubscriptions.Settings;

public class GoTrackSettingKeys
{
    private const string Prefix = "GoTrack.TrackAccountSubscription.";
    public static string GracePeriod = $"{Prefix}GracePeriod";
    public static int GracePeriodDefaultValue = 0;

    public static string NotificationDays = $"{Prefix}NotificationDays";
    public static List<int> NotificationDaysDefaultValue = [15, 10, 5];

    public static string SmsLowCountThreshold = $"{Prefix}SmsLowCountThreshold";
    public static int SmsLowCountThresholdDefaultValue = 20;
}