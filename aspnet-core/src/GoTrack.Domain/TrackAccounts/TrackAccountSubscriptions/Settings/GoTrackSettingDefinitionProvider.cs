using Volo.Abp.Settings;

namespace GoTrack.TrackAccounts.TrackAccountSubscriptions.Settings;

public class GoTrackSettingDefinitionProvider : SettingDefinitionProvider
{
    public override void Define(ISettingDefinitionContext context)
    {
        context.Add(
            new SettingDefinition(
                GoTrackSettingKeys.GracePeriod,
                GoTrackSettingKeys.GracePeriodDefaultValue.ToString(),
                isVisibleToClients: true,
                isEncrypted: false)
        );
        context.Add(
            new SettingDefinition(
                GoTrackSettingKeys.NotificationDays,
                string.Join(",", GoTrackSettingKeys.NotificationDaysDefaultValue.ToArray()),
                isVisibleToClients: true,
                isEncrypted: false)
        );
        context.Add(
            new SettingDefinition(
                GoTrackSettingKeys.SmsLowCountThreshold,
                GoTrackSettingKeys.SmsLowCountThresholdDefaultValue.ToString(),
                isVisibleToClients: true,
                isEncrypted: false)
        );
    }
}