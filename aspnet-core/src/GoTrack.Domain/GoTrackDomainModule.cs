using GoTrack.BlobContainers;
using GoTrack.FCMDevices;
using GoTrack.MultiTenancy;
using GoTrack.Payments.Discounts;
using GoTrack.Payments.Discounts.Specifications;
using GoTrack.TrackAccounts.TrackAccountSubscriptions.SubscriptionFeatures;
using GoTrack.TrackAccounts.TrackAccountSubscriptions.Workers;
using MailKit.Security;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Notify;
using Notify.Provider.Email;
using Notify.Provider.FCM;
using Notify.Provider.Sms;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.AuditLogging;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.BlobStoring;
using Volo.Abp.BlobStoring.Database;
using Volo.Abp.Emailing;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Features;
using Volo.Abp.Identity;
using Volo.Abp.Localization;
using Volo.Abp.MailKit;
using Volo.Abp.Modularity;
using Volo.Abp.MultiTenancy;
using Volo.Abp.OpenIddict;
using Volo.Abp.PermissionManagement.Identity;
using Volo.Abp.PermissionManagement.OpenIddict;
using Volo.Abp.SettingManagement;
using Volo.Abp.Sms;
using Volo.Abp.TenantManagement;

namespace GoTrack;

[DependsOn(
    typeof(GoTrackDomainSharedModule),
    typeof(AbpAuditLoggingDomainModule),
    typeof(AbpBackgroundJobsDomainModule),
    typeof(AbpFeatureManagementDomainModule),
    typeof(AbpIdentityDomainModule),
    typeof(AbpOpenIddictDomainModule),
    typeof(AbpPermissionManagementDomainOpenIddictModule),
    typeof(AbpPermissionManagementDomainIdentityModule),
    typeof(AbpSettingManagementDomainModule),
    typeof(AbpTenantManagementDomainModule),
    typeof(AbpMailKitModule),
    typeof(AbpBlobStoringModule),
    typeof(BlobStoringDatabaseDomainModule),
    typeof(AbpSmsModule),
    typeof(NotifyProviderSmsModule),
    typeof(NotifyProviderFCMModule),
    typeof(NotifyProviderEmailModule),
    typeof(NotifyDomainModule),
    typeof(AbpBackgroundWorkersModule)
)]
    public class GoTrackDomainModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddTransient<IDiscountSpecification, DurationDiscountSpecification>();
        context.Services.AddTransient<IDiscountSpecification, PricingItemDiscountSpecification>();
        context.Services.AddTransient<IDiscountSpecification, RequestIdDiscountSpecification>();
        context.Services.AddTransient<IDiscountSpecification, RequestTypeDiscountSpecification>();
        context.Services.AddTransient<IDiscountSpecification, UserCountDiscountSpecification>();
        context.Services.AddTransient<IDiscountSpecification, PromoCodeDiscountSpecification>();
        
        Configure<AbpLocalizationOptions>(options =>
        {
            options.Languages.Add(new LanguageInfo("ar", "ar", "العربية"));
            options.Languages.Add(new LanguageInfo("cs", "cs", "Čeština"));
            options.Languages.Add(new LanguageInfo("en", "en", "English"));
            options.Languages.Add(new LanguageInfo("en-GB", "en-GB", "English (UK)"));
            options.Languages.Add(new LanguageInfo("hu", "hu", "Magyar"));
            options.Languages.Add(new LanguageInfo("hr", "hr", "Croatian"));
            options.Languages.Add(new LanguageInfo("fi", "fi", "Finnish"));
            options.Languages.Add(new LanguageInfo("fr", "fr", "Français"));
            options.Languages.Add(new LanguageInfo("hi", "hi", "Hindi"));
            options.Languages.Add(new LanguageInfo("it", "it", "Italiano"));
            options.Languages.Add(new LanguageInfo("pt-BR", "pt-BR", "Português"));
            options.Languages.Add(new LanguageInfo("ru", "ru", "Русский"));
            options.Languages.Add(new LanguageInfo("sk", "sk", "Slovak"));
            options.Languages.Add(new LanguageInfo("tr", "tr", "Türkçe"));
            options.Languages.Add(new LanguageInfo("zh-Hans", "zh-Hans", "简体中文"));
            options.Languages.Add(new LanguageInfo("zh-Hant", "zh-Hant", "繁體中文"));
            options.Languages.Add(new LanguageInfo("de-DE", "de-DE", "Deutsch"));
            options.Languages.Add(new LanguageInfo("es", "es", "Español"));
        });

        Configure<AbpMultiTenancyOptions>(options =>
        {
            options.IsEnabled = MultiTenancyConsts.IsEnabled;
        });

#if DEBUG
        context.Services.Replace(ServiceDescriptor.Singleton<IEmailSender, NullEmailSender>());
        context.Services.Replace(ServiceDescriptor.Singleton<ISmsSender, NullSmsSender>());
#endif


        Configure<AbpFeatureOptions>(options =>
        {
            options.ValueProviders.Add<TrackAccountSubscriptionFeatureValueProvider>();
        });
        Configure<FeatureManagementOptions>(options =>
        {
            options.Providers.Add<TrackAccountSubscriptionFeatureManagementProvider>();
        });
        
        Configure<AbpBlobStoringOptions>(options =>
        {
            options.Containers.Configure<CustomerProfilePictureContainer>(container =>
            {
                container.UseDatabase();
            });
        });
        
        Configure<AbpMailKitOptions>(options =>
        {
            options.SecureSocketOption = SecureSocketOptions.Auto;
        });

    }

    public override async Task OnApplicationInitializationAsync(
        ApplicationInitializationContext context)
    {
        await context.AddBackgroundWorkerAsync<CleanupInactiveDevicesWorker>();
        await context.AddBackgroundWorkerAsync<ExpireTrackAccountSubscriptionWorker>();
        await context.AddBackgroundWorkerAsync<NotifySubscriptionWorker>();
    }
}
