using System;
using System.Threading.Tasks;
using GoTrack.Addresses;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.Identity;

namespace GoTrack.Identity;

public class IdentityUserProfile : FullAuditedAggregateRoot<Guid>
{
    public Guid UserId { get; private set; }
    public Address Address { get; private set; }
    // navigations
    public IdentityUser User { get; private set; }

    private IdentityUserProfile()
    {
    }

    internal IdentityUserProfile(IdentityUser user, Address address) : base(user.Id)
    {
        UserId = user.Id;
        Address = address;
    }

    public void ChangeAddress(Address address)
    {
        Address = address;
    }

    public string GetProfilePictureKey()
    {
        return Id.ToString();
    }
    
}