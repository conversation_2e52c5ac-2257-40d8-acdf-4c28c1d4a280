using GoTrack.TrackAccounts;
using System;
using System.Drawing;
using Volo.Abp.Domain.Entities.Auditing;
using Point = NetTopologySuite.Geometries.Point;

namespace GoTrack.StopPoints;

public class StopPoint : FullAuditedAggregateRoot<Guid>, IHaveTrackAccount
{
    public string Name { get; private set; }
    public Point Point { get; private set; }
    public Color Color { get; private set; }

    public Guid TrackAccountId { get; private set; }

    private StopPoint() { }

    public StopPoint(
        Guid id,
        string name,
        Point point,
        Color color,
        Guid trackAccountId) 
        : base(id)
    {
        Name = name;
        Point = point;
        Color = color;
        TrackAccountId = trackAccountId;
    }
}
