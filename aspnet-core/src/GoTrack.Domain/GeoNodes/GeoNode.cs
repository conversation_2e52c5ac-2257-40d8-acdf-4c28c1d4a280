using System;
using Volo.Abp;
using Volo.Abp.Domain.Entities;

namespace GoTrack.GeoNodes;

public class GeoNode : AggregateRoot<Guid>
{
    public string Name { get; private set; }
    public Guid TreeId { get; private set; }
    public Guid? ParentNodeId { get; private set; }
    public GeoNodeType NodeType { get; private set; }

    private GeoNode()
    {
    }
    
    internal GeoNode(Guid id, string name, Guid treeId, GeoNodeType nodeType, Guid? parentNodeId) : base(id)
    {
        Name = Check.NotNullOrEmpty(name, nameof(name));
        TreeId = treeId;
        NodeType = nodeType;
        ParentNodeId = parentNodeId;
    }
}