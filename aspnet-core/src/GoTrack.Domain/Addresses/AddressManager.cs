using System.Threading.Tasks;
using GoTrack.GeoNodes;
using Volo.Abp.Domain.Services;

namespace GoTrack.Addresses;

public class AddressManager : DomainService, IAddressManager
{
    private readonly IGeoNodeManager _geoNodeManager;

    public AddressManager(IGeoNodeManager geoNodeManager)
    {
        _geoNodeManager = geoNodeManager;
    }

    public async Task<Address> CreateAsync(string street, string area, string city, string governorate,
        string country = GoTrackConsts.DefaultCountryName)
    {
        await _geoNodeManager.VerifyAddressAsync(country, governorate, city);

        return new Address(street, area, city, governorate, country);
    }
}