using System.Collections.Generic;
using Volo.Abp;
using Volo.Abp.Domain.Values;

namespace GoTrack.Addresses;

public class Address : ValueObject
{
    public string Street { get; private set; }
    public string Area { get; private set; }
    public string City { get; private set; }
    public string Governorate { get; private set; }
    public string Country { get; private set; }

    private Address()
    {
    }

    internal Address(string street, string area, string city, string governorate, string country)
    {
        Street = Check.NotNullOrEmpty(street, nameof(street));
        Area = Check.NotNullOrEmpty(area, nameof(area));
        City = Check.NotNullOrEmpty(city, nameof(city));
        Governorate = Check.NotNullOrEmpty(governorate, nameof(governorate));
        Country = Check.NotNullOrEmpty(country, nameof(country));
    }

    protected override IEnumerable<object> GetAtomicValues()
    {
        yield return Street;
        yield return Area;
        yield return City;
        yield return Governorate;
        yield return Country;
    }
}