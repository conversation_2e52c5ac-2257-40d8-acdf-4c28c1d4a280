create database gps21_check_stop_in_zone default charset = utf8mb4;

CREATE TABLE `alert_list` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `affective_from` datetime NOT NULL,
    `last_checked_at` varchar(20) NOT NULL,
    `imei` varchar(20) NOT NULL,
    `polygon` TEXT NOT NULL,
    `is_alerted` int(11) NOT NULL,
    `sat_from` int(11) NOT NULL DEFAULT '0',
    `sat_to` int(11) NOT NULL DEFAULT '0',
    `sun_from` int(11) NOT NULL DEFAULT '0',
    `sun_to` int(11) NOT NULL DEFAULT '0',
    `mon_from` int(11) NOT NULL DEFAULT '0',
    `mon_to` int(11) NOT NULL DEFAULT '0',
    `tue_from` int(11) NOT NULL DEFAULT '0',
    `tue_to` int(11) NOT NULL DEFAULT '0',
    `wed_from` int(11) NOT NULL DEFAULT '0',
    `wed_to` int(11) NOT NULL DEFAULT '0',
    `thu_from` int(11) NOT NULL DEFAULT '0',
    `thu_to` int(11) NOT NULL DEFAULT '0',
    `fri_from` int(11) NOT NULL DEFAULT '0',
    `fri_to` int(11) NOT NULL DEFAULT '0',
    `last_alerted_at` varchar(20) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB;

CREATE VIEW `grouped_alert_list` AS 
    select `imei` AS `imei`,`polygon` AS `polygon`,group_concat(`alert_list`.`id` separator ',') AS `ids` 
    from `alert_list` 
    group by `imei`,`polygon`,substr(`alert_list`.`last_checked_at`,1,7) 
    order by `imei`,`polygon`,substr(`alert_list`.`last_checked_at`,1,7);