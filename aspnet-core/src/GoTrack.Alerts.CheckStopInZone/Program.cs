using GoTrack.Alerts.BaseChecker;
using GoTrack.Alerts.Contracts.Exchanges.IAlertCrud;
using MassTransit;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Warp10;

namespace GoTrack.Alerts.CheckStopInZone;

public class Program
{
    public static void Main(string[] args)
    {
        CreateHostBuilder(args).Build().Run();
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureServices((hostContext, services) =>
            {
                services.AddScoped<IRepoService, RepoServiceImpl>();
                services.AddScoped(x => 
                    new WarpService(
                        hostContext.Configuration["Warp10:Url"],
                        hostContext.Configuration["Warp10:ReadToken"],
                        hostContext.Configuration["Warp10:WriteToken"],
                    LoggerFactory.Create(logging => logging.AddConsole())
                        .CreateLogger<WarpService>()
                    )
                );

                services.AddDbContext<GoTrackAlertCheckStopInZoneContext>(options => options
                    .UseMySql(
                        hostContext.Configuration.GetConnectionString("DefaultConnection"),
                        ServerVersion.AutoDetect(
                            hostContext.Configuration.GetConnectionString("DefaultConnection")
                        )
                    )
                );


                services.AddScoped<IScopedProcessingService, ScopedProcessingService>();
                services.AddHostedService<ConsumeScopedServiceHostedService>();

                string serviceCode = hostContext.Configuration.GetValue<string>("InstanceCode");
                services.AddMassTransit(x =>
                {
                    x.AddConsumer<AlertsCrudConsumer>(x => { x.UseConcurrencyLimit(1); });
                    x.UsingRabbitMq((context, cfg) =>
                    {
                        cfg.Host(
                            hostContext.Configuration["RabbitMQ:Host"],
                            hostContext.Configuration["RabbitMQ:VirtualHost"], 
                            h =>
                            {
                                h.Username(hostContext.Configuration["RabbitMQ:Username"]);
                                h.Password(hostContext.Configuration["RabbitMQ:Password"]);
                            });
                        
                        cfg.ReceiveEndpoint("stop_in_zone_service_" + serviceCode, e =>
                        {
                            e.PrefetchCount = 1;
                            e.Bind<AlertCrudToService>(x =>
                            {
                                x.ExchangeType = "direct";
                                x.RoutingKey = serviceCode;
                            });
                            e.ConfigureConsumer<AlertsCrudConsumer>(context);
                            e.ConfigureConsumeTopology = false;
                        });
                        
                        cfg.ConfigureEndpoints(context);
                    });
                });
            });
}