namespace GoTrack.Permissions;

public static class GoTrackPermissions
{
    public const string GroupName = "GoTrack";

    //Add your own permission names. Example:
    //public const string MyPermission1 = GroupName + ".MyPermission1";

    public const string BusinessAccountSubscriptionRequestGroupName = GroupName + ".BusinessAccountSubscriptionRequestManagement";
    public const string BusinessAccountSubscriptionRequestIndex = BusinessAccountSubscriptionRequestGroupName + ".Index";
    public const string BusinessAccountSubscriptionRequestDetails = BusinessAccountSubscriptionRequestGroupName + ".GetDetails";
    public const string BusinessAccountSubscriptionRequestStartProcessing = BusinessAccountSubscriptionRequestGroupName + ".StartProcessing";
    public const string BusinessAccountSubscriptionRequestInstallDevices = BusinessAccountSubscriptionRequestGroupName + ".InstallDevices";
    public const string BusinessAccountSubscriptionRequestFinishProcessing = BusinessAccountSubscriptionRequestGroupName + ".FinishProcessing";
    public const string BusinessAccountSubscriptionRequestReject = BusinessAccountSubscriptionRequestGroupName + ".Reject";
    public const string BusinessAccountSubscriptionRequestApplyDiscount = BusinessAccountSubscriptionRequestGroupName + ".ApplyDiscount";

    public const string PersonalAccountSubscriptionRequestGroupName = GroupName + ".PersonalAccountSubscriptionRequestManagement";
    public const string PersonalAccountSubscriptionRequestIndex = PersonalAccountSubscriptionRequestGroupName + ".Index";
    public const string PersonalAccountSubscriptionRequestDetails = PersonalAccountSubscriptionRequestGroupName + ".GetDetails";
    public const string PersonalAccountSubscriptionRequestStartProcessing = PersonalAccountSubscriptionRequestGroupName + ".StartProcessing";
    public const string PersonalAccountSubscriptionRequestInstallDevices = PersonalAccountSubscriptionRequestGroupName + ".InstallDevices";
    public const string PersonalAccountSubscriptionRequestFinishProcessing = PersonalAccountSubscriptionRequestGroupName + ".FinishProcessing";
    public const string PersonalAccountSubscriptionRequestReject = PersonalAccountSubscriptionRequestGroupName + ".Reject";
    public const string PersonalAccountSubscriptionRequestApplyDiscount = PersonalAccountSubscriptionRequestGroupName + ".ApplyDiscount";

    public const string RequestGroupName = GroupName + ".RequestManagement";
    public const string RequestIndex = RequestGroupName + ".Index";
    public const string RequestNoteIndex = RequestGroupName + ".NoteIndex";
    public const string RequestPayWithCash = RequestGroupName + ".PayWithCash";


    public const string DeviceGroupName = GroupName + ".DeviceManagement";
    public const string DeviceCreate = DeviceGroupName + ".Create";
    public const string DeviceDetails = DeviceGroupName + ".GetDetails";
    public const string DeviceIndex = DeviceGroupName + ".Index";
    public const string GetDeactivatedDevicesByUser = DeviceGroupName + ".GetDeactivatedDevicesByUser";

    public const string TrackAccountGroupName = GroupName + ".TrackAccountManagement";
    public const string TrackAccountIndex = TrackAccountGroupName + ".Index";
    public const string TrackAccountFeaturesDetails = TrackAccountGroupName + ".GetFeaturesDetails";
    public const string SetFeatureForTrackAccount = TrackAccountGroupName + ".SetFeature";
    public const string TrackAccountDetails = TrackAccountGroupName + ".GetDetails";
    public const string TrackAccountUserAssociations = TrackAccountGroupName + ".UserAssociationsIndex";
    public const string TrackAccountSubscriptions = TrackAccountGroupName + ".GetSubscriptions";


    public const string FeatureGroupName = GroupName + ".FeatureManagement";
    public const string FeatureIndex = FeatureGroupName + ".Index";

    public const string AlertLogGroupName = GroupName + ".AlertLogManagement";
    public const string AlertLogIndex = AlertLogGroupName + ".Index";

    public const string SmsBundleGroupName = GroupName + ".SmsBundleManagement";
    public const string SmsBundleCreate = SmsBundleGroupName + ".Create";
    public const string SmsBundleDetails = SmsBundleGroupName + ".GetDetails";
    public const string SmsBundleIndex = SmsBundleGroupName + ".Index";
    public const string SmsBundleUpdate = SmsBundleGroupName + ".Update";
    public const string SmsBundleDelete = SmsBundleGroupName + ".Delete";

    public const string RenewSubscriptionRequestGroupName = GroupName + ".RenewSubscriptionRequestManagement";
    public const string RenewSubscriptionRequestIndex = RenewSubscriptionRequestGroupName + ".Index";
    public const string RenewSubscriptionRequestDetails = RenewSubscriptionRequestGroupName + ".GetDetails";
    public const string RenewSubscriptionRequestAcceptRequest = RenewSubscriptionRequestGroupName + ".AcceptRequest";
    public const string RenewSubscriptionRequestInstallDevices = RenewSubscriptionRequestGroupName + ".InstallDevices";
    public const string RenewSubscriptionRequestFinishProcessing = RenewSubscriptionRequestGroupName + ".FinishProcessing";
    public const string RenewSubscriptionRequestReject = RenewSubscriptionRequestGroupName + ".Reject";
    public const string RenewSubscriptionRequestRemovedVehicles = RenewSubscriptionRequestGroupName + ".IndexRemovedVehicles";
    public const string RenewSubscriptionRequestNewVehicles = RenewSubscriptionRequestGroupName + ".IndexNewVehicles";
    public const string RenewSubscriptionRequestRemovedUsers = RenewSubscriptionRequestGroupName + ".IndexRemovedUsers";
    public const string RenewSubscriptionRequestRemainingVehicles = RenewSubscriptionRequestGroupName + ".IndexRemainingVehicles";
    public const string RenewSubscriptionRequestGetListNewVehiclesWithSearch = RenewSubscriptionRequestGroupName + ".IndexAndSerachNewVehicles";

    public const string IncreaseUserCountRequestGroupName = GroupName + ".IncreaseUserCountRequestManagement";
    public const string IncreaseUserCountRequestIndex = IncreaseUserCountRequestGroupName + ".Index";
    public const string IncreaseUserCountRequestDetails = IncreaseUserCountRequestGroupName + ".Details";

    public const string RenewSMSPackageRequestGroupName = GroupName + ".RenewSMSPackageRequestManagement";
    public const string RenewSMSPackageRequestIndex = RenewSMSPackageRequestGroupName + ".Index";

    public const string AddVehiclesRequestGroupName = GroupName + ".AddVehiclesRequestManagement";
    public const string AddVehiclesRequestIndex = AddVehiclesRequestGroupName + ".Index";
    public const string AddVehiclesRequestDetails = AddVehiclesRequestGroupName + ".GetDetails";
    public const string AddVehiclesRequestInstallDevices = AddVehiclesRequestGroupName + ".InstallDevices";
    public const string AddVehiclesRequestFinishProcessing = AddVehiclesRequestGroupName + ".FinishProcessing";
    public const string AddVehiclesRequestGetListVehiclesWithSearch = AddVehiclesRequestGroupName + ".IndexAndSearchVehicles";


    public const string PricingManagementGroupName = GroupName + ".PricingManagement";
    public const string PricingSet = PricingManagementGroupName + ".Set";
    public const string PricingIndex = PricingManagementGroupName + ".Index";

    public const string SubscriptionDurationDiscountGroupName = GroupName + ".SubscriptionDurationDiscountManagement";
    public const string SubscriptionDurationDiscountIndex = SubscriptionDurationDiscountGroupName + ".Index";
    public const string SubscriptionDurationDiscountCreate = SubscriptionDurationDiscountGroupName + ".Create";
    public const string SubscriptionDurationDiscountDelete = SubscriptionDurationDiscountGroupName + ".Delete";

    public const string DiscountGroupName = GroupName + ".DiscountManagement";
    public const string DiscountIndex = DiscountGroupName + ".Index";
    public const string DiscountCreate = DiscountGroupName + ".Create";
    public const string DiscountDelete = DiscountGroupName + ".Delete";

    public const string PromoCodeGroupName = GroupName + ".PromoCode";
    public const string PromoCodeCreate = PromoCodeGroupName + ".Create";
    public const string PromoCodeUpdateRange = PromoCodeGroupName + ".UpdateRange";
    public const string PromoCodeDeactive = PromoCodeGroupName + ".Deactive";
    public const string PromoCodeDetails = PromoCodeGroupName + ".Details";
    public const string PromoCodeIndex = PromoCodeGroupName + ".Index";

    public const string TrackAccountSubscriptionGroupName = GroupName + ".TrackAccountSubscriptionManagement";
    public const string TrackAccountSubscriptionIndex = TrackAccountSubscriptionGroupName + ".Index";
    public const string TrackAccountSubscriptionDetails = TrackAccountSubscriptionGroupName + ".Details";

    public const string SettingsGroupName = GroupName + ".SettingsManagement";
    public const string TrackAccountSubscriptionExpireReminderGet = SettingsGroupName + ".TrackAccountSubscriptionExpireReminder.Get";
    public const string TrackAccountSubscriptionExpireReminderUpdate = SettingsGroupName + ".TrackAccountSubscriptionExpireReminder.Update";
    public const string SmsBundleExpireReminderGet = SettingsGroupName + ".SmsBundleExpireReminder.Get";
    public const string SmsBundleExpireReminderUpdate = SettingsGroupName + ".SmsBundleExpireReminder.Update";

}