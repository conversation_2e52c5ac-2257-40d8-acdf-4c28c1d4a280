using System;
using GoTrack.RenewTrackAccountSubscriptions;
using GoTrack.SubscriptionPlans;

namespace GoTrack.Requests.RenewSubscriptionRequests.DTOs;

public class RenewSubscriptionRequestDto :RequestDto
{
    public Guid TrackAccountId { get; set; }
    public SubscriptionPlan SubscriptionPlan { get; set; }
    public  Guid TrackAccountSubscriptionId { get; set; }
    public int UserCount { get;set; }
    public Guid? SmsBundleId { get; set; }
    public int SubscriptionDurationInMonths { get; set; }
    public decimal? DiscountRate { get; set; }
    public RenewSubscriptionRequestStage RenewSubscriptionRequestStage { get; set; }
    public int NewTrackVehiclesCount { get;  set; }
}