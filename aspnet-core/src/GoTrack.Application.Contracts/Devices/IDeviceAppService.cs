using System;
using System.Threading.Tasks;
using GoTrack.Devices.DTOs;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace GoTrack.Devices;

public interface IDeviceAppService : IApplicationService
{
     Task<DeviceDetailsDto> GetAsync(Guid id);
     Task<Guid> CreateAsync(CreateDeviceDto deviceDto);
     Task<PagedResultDto<DeviceDto>> GetListAsync(PagedResultRequestDto input);
     Task<PagedResultDto<AvailableDevicesForInstallationDeviceDto>> GetAvailableDevicesForInstallationAsync(PagedResultRequestDto input);
    Task<PagedResultDto<DeactivatedDevicesDto>> GetDeactivatedDevicesByUserAsync(GetDeactivatedDevicesByUserDto input);
}