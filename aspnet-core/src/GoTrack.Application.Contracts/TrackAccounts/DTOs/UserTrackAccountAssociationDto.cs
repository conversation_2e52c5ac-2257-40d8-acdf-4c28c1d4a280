using GoTrack.UserTrackAccountAssociations;
using System;
using Volo.Abp.Application.Dtos;

namespace GoTrack.TrackAccounts.DTOs;

public class UserTrackAccountAssociationDto : EntityDto<Guid>
{
    public Guid? UserId { get; set; }
    public Guid TrackAccountId { get; set; }
    public AssociationType AssociationType { get; set; }
    public string? PhoneNumber { get; set; }
    public string Name { get; set; } = string.Empty;
    public UserTrackAccountAssociationStatus Status { get; set; }
}
