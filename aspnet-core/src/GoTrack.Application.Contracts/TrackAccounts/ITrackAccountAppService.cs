using GoTrack.TrackAccounts.DTOs;
using GoTrack.TrackAccounts.TrackAccountSubscriptions.DTOs;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.FeatureManagement;

namespace GoTrack.TrackAccounts;

public interface ITrackAccountAppService : IApplicationService
{
    Task<List<FeatureDto>> GetFeaturesAsync(Guid id);
    Task<PagedResultDto<TrackAccountDto>> GetListAsync(PagedResultRequestDto input);
    //Task<List<FeatureDto>> GetAvailableFeaturesAsync();
    Task<PagedResultDto<UserTrackAccountAssociationDto>> GetUserTrackAccountAssociationsAsync(Guid id, GetUserTrackAccountAssociationsDto input);
    Task<BusinessTrackAccountDetailsDto> GetBusinessTrackAccountDetailsAsync(Guid id);
    Task<PersonalTrackAccountDetailsDto> GetPersonalTrackAccountDetailsAsync(Guid id);
    Task<PagedResultDto<TrackAccountSubscriptionDto>> GetSubscriptionsAsync(Guid id, GetTrackAccountSubscriptionDto input);
    //Task SetFeatureForTrackAccountAsync(SetFeatureForTrackAccountDto input);
}
