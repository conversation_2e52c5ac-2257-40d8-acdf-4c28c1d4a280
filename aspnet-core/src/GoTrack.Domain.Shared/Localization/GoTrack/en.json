{"culture": "en", "texts": {"Menu:Home": "Home", "Welcome": "Welcome", "LongWelcomeMessage": "Welcome to the application. This is a startup project based on the ABP framework. For more information, visit abp.io.", "Enum:VehicleLicensePlateSubClass.0": "Damascus", "Enum:VehicleLicensePlateSubClass.1": "Aleppo", "Enum:VehicleLicensePlateSubClass.2": "<PERSON><PERSON><PERSON>", "Enum:VehicleLicensePlateSubClass.3": "AsSuwayda", "Enum:VehicleLicensePlateSubClass.4": "<PERSON><PERSON>", "Enum:VehicleLicensePlateSubClass.5": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Enum:VehicleLicensePlateSubClass.6": "<PERSON><PERSON>", "Enum:VehicleLicensePlateSubClass.7": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Enum:VehicleLicensePlateSubClass.8": "Homs", "Enum:VehicleLicensePlateSubClass.9": "<PERSON><PERSON><PERSON>", "Enum:VehicleLicensePlateSubClass.10": "Latakia", "Enum:VehicleLicensePlateSubClass.11": "Quneitra", "Enum:VehicleLicensePlateSubClass.12": "RifDimashq", "Enum:VehicleLicensePlateSubClass.13": "<PERSON><PERSON><PERSON>", "Enum:RequestStatus.0": "Pending", "Enum:RequestStatus.1": "Processing", "Enum:RequestStatus.2": "Processed", "Enum:RequestStatus.3": "Rejected", "Enum:RequestStatus.4": "Canceled", "GoTrack:VehicleNotInFleet": "Vehicle not in fleet", "GoTrack:FromDate": "From Date", "GoTrack:ToDate": "To Date", "GoTrack:IgnoreSpeedUnder": "Ignore Speed Under", "GoTrack:ConsumptionRate": "Consumption Rate", "GoTrack:Color": "Color", "GoTrack:CompanyName": "Company Name", "GoTrack:Address": "Address", "GoTrack:AccountName": "Account Name", "GoTrack:Name": "Name", "GoTrack:PhoneNumber": "Phone Number", "GoTrack:VehicleIds": "Vehicles", "GoTrack:VehicleGroupIds": "Vehicle Groups", "GoTrack:UserTrackAccountAssociationId": "User track account association Id", "GoTrack:GeoZoneIds": "GeoZone", "GoTrack:NotificationMethods": "Notification Methods", "GoTrack:MaxSpeed": "Max Speed", "GoTrack:RouteIds": "Routes", "GoTrack:Line": "Line", "GoTrack:StopPoints": "Stop points", "GoTrack:TripTemplateId": "Trip Template", "GoTrack:RequestId": "Request", "GoTrack:StartTime": "Start Time", "GoTrack:EndTime": "End Time", "GoTrack:DaysOfWeek": "Days Of Week", "GoTrack:Value": "Value", "GoTrack:TrackAccount": "TrackAccount", "GoTrack:ObserversList": "Identified Observers", "GoTrack:AddObserver": "Add Observer", "GoTrack:AssignedVehiclesAndGroups": "View Assigned Vehicles and Groups", "GoTrack:Observers": "The Observers", "GoTrack:UserId": "User", "GoTrack:AccountType": "Account type", "GoTrack:AccountDetails": "Account Details", "GoTrack:TrackAccountManagement": "Track Accounts Management", "GoTrack:AccountType.Business": "Business", "GoTrack:AccountType.Personal": "Personal", "GoTrack:SubscriptionPlan": "Subscription Plan", "GoTrack:UserCount": "Number of Watchers", "GoTrack:SmsBundleCount": "Number of SMS Messages", "GoTrack:From": "Subscription Start Date", "GoTrack:To": "Subscription End Date", "GoTrack:State": "State", "GoTrack:State.Active": "Active", "GoTrack:State.Pending": "Pending", "GoTrack:State.Expired": "Expired", "GoTrack:Status": "Status", "GoTrack:AssociationType": "Association Type", "GoTrack:SubscriptionDetails": "Subscription Details", "GoTrack:PersonalSubscriptionRequestDetails": "Personal Subscription Request Details", "GoTrack:BusinessSubscriptionRequestDetails": "Business Subscription Request Details", "GoTrack:State.Basic": "Basic", "GoTrack:State.Standard": "Standard", "GoTrack:State.Premium": "Premium", "RenewSubscriptionRequest:SmsBundleName": "SMS Bundle", "GoTrack:SubscriptionDurationInMonths": "Subscription Duration In Months", "GoTrack:DiscountRate": "Discount Rate", "GoTrack:DiscountValue": "Discount Value", "GoTrack:EnteringZoneMessage": "Vehicle with serial number '{0}' has entered the '{1}' zone at '{2}'", "GoTrack:ExitingZoneMessage": "Vehicle with serial number '{0}' has exited the '{1}' zone at '{2}'", "GoTrack:OverSpeedMessage": "Vehicle with serial number '{0}' exceeded the speed limit '{1} at '{2}''", "GoTrack:ExitingRouteMessage": "Vehicle with serial number '{0}' has exited the '{1}' route at '{2}'", "GoTrack:VehicleAlert": "Vehicle Alert", "GoTrack:Stage": "Stage", "GoTrack:JobTimeMessage": "Vehicle with serial number {0} operated outside assigned working hours at '{1}'", "GoTrack:DisassembleTrackingDeviceMessage": "Vehicle with plate {0} has disassembled the tracking device at '{1}'", "Device:model": "Model", "Device:brand": "Brand", "Device:imei": "<PERSON><PERSON>", "Device:sim": "<PERSON>m", "Device:protocol": "Protocol", "protocol": "Protocol", "Device:status": "Status", "DevicesManagement": "Devices Management", "AddNewDevice": "Add New Device", "Device:modelAr": "Model(Ar)", "Device:modelEn": "Model(En)", "Device:brandAr": "<PERSON>(Ar)", "Device:brandEn": "<PERSON>(En)", "Device:StatusLog": "Status Log", "Device:date": "Date", "SuccessCreate": "Creation Done Successfully", "SuccessOperation": "Done Successfully", "VehicleDevice:deviceId": "<PERSON><PERSON>", "VehicleDevice:vehicleId": "Vehicle", "LinkVehiclesToDevices": "Link Vehicles To Devices", "BusinessAccountSubscription": "Business", "PersonalAccountSubscription": "Personal", "SubscriptionRequestManagement": "Subscription Request Management", "LiveMonitoring": "Live Monitoring", "ShowVehiclePath": "Show Vehicle Path", "Notifications": "Notifications", "ShareTheSite": "Share The Site", "Alerts": "<PERSON><PERSON><PERSON>", "AlarmDisassemblyDevice": "Alarm Disassembly Device", "SendTextMessageOnEveryAlarm": "Send Text Message On Every Alarm", "OvertimeWarning": "Overtime Warning", "Reports": "Reports", "ExportReports": "Export Reports", "GeographicAreaManagement": "Geographic Area Management", "PathManagement": "Path Management", "StopPointManagement": "Stop Point Management", "MonitorSpecificEventsRelatedToTheVehiclesCondition": "Monitor Specific Events Related To The Vehicle's Condition", "GroupManagement": "Group Management", "ObserverManagement": "Observer Management", "Dashboard": "Dashboard", "Business": "Business", "Personal": "Personal", "add new Route": "Add New Route", "remove": "Remove", "add new point": "Add New Stop Point", "L/h": "L/h", "Km/h": "Km/h", "change view": "Change View", "now": "Now", "hour": "Hour", "day": "Day", "hour24": "Hours 24", "main": "Main", "notifications": "Notifications", "services": "Services", "report": "Report", "more": "More", "averageSpeed": "Average Speed", "consumptionRate": "Fuel Consumption Rate", "vehicle": "Vehicle", "edit": "Edit", "delete": "Delete", "Pending": "Pending", "Processing": "Processing", "Processed": "Processed", "Rejected": "Rejected", "Canceled": "Canceled", "Review": "Review", "Payment": "Payment", "PaymentReview": "Payment Review", "DeviceInstallation": "Device Installation", "Finished": "Finished", "Basic": "Basic", "Standard": "Standard", "Premium": "Premium", "Owner": "Owner", "Observer": "Observer", "SP": "SP", "ServiceCenter": "Service Center", "OnSite": "On Site", "OtpMessage": "Your verification code is {0}", "TrackAccount:Subscriptions": "Subscriptions", "TrackAccount:UserAssociations": "User Associations", "DiscountRate": "Discount rate", "vehiclePlate": "Vehicle Plate", "availableDevicesForInstallation": "Available Devices For Installation", "deactivedDevicesByUser": "Deactived Devices By User", "needsTrackingDevice": "Needs Tracking Device", "PricingItemKeys:Device": "<PERSON><PERSON>", "PricingItemKeys:DeviceInstallation": "Device Installation", "PricingItemKeys:DeviceDataSim": "Device Data SIM", "PricingItemKeys:AdditionalUsers": "Additional Users", "PricingItemKeys:SmsBundle": "SMS Bundle", "PricingItemKeys:SubscriptionsBasic": "Basic Vehicle Subscription", "PricingItemKeys:SubscriptionsStandard": "Standard Vehicle Subscription", "PricingItemKeys:SubscriptionsPremium": "Premium Vehicle Subscription", "PricingItemKeys:VehicleTrackingMonthlyFee": "Vehicle Tracking Monthly Fee", "ExpirationNotification": "Your subscription will expire in {0} days. Please renew your subscription to continue using our services without interruption.", "EnterNumberBetween0And100": "Enter a number between 0 and 100", "EnterNumberBetween0And1": "Enter a number between 0 and 1", "ValueMustBeGreaterThanZero": "The value must be greater than zero", "ValueMustBeLessThan100": "The value must be less than 100", "AccountsRequestsManagement": "Accounts Requests Management", "GoTrack:IncreaseObserversCountRequests": "Requests of Increase Observers Count", "GoTrack:IncreaseUserCountRequestStage": "Stage", "GoTrack:Details": "Details", "increaseUserCountRequestStage": "Stage", "IncreaseObserversCountRequests:OwnerFirstName": "Subscriber's phone number", "IncreaseObserversCountRequests:OwnerPhoneNumber": "Subscriber's name", "IncreaseObserversCountRequests:OwnerEmail": "Subscriber's email", "IncreaseObserversCountRequests:TrackAccountName": "Tracking account name", "IncreaseObserversCountRequests:TrackAccountSubscriptionFrom": "Subscription start date", "IncreaseObserversCountRequests:TrackAccountSubscriptionTo": "Subscription end date", "IncreaseObserversCountRequests:UserCount": "Number of additional observers", "IncreaseObserversCountRequests:CreationTime": "Application submission date", "GoTrack:RenewSMSPackageRequest": "Renew SMS Package Requests", "GoTrack:RenewSubscriptionRequest": "Renew Subscription Requests", "GoTrack:RenewSubscriptionRequestDetails": "Renew Subscription Request Details", "RenewSubscriptionRequest:RenewSubscriptionRequestStage": "Stage", "Request:Accept": "Reject Request", "RenewSubscriptionRequest:NewlyAddedVehicles": "Newly added vehicles", "RenewSubscriptionRequest:PreviousSubscriptionVehicles": "Vehicles added from previous subscription", "RenewSubscriptionRequest:RemoveUsers": "Removed Observers", "RenewSubscriptionRequest:RemoveTrackVehicles": "Removed Track Vehicles", "RenewSubscriptionRequest:OwnerFirstName": "Subscriber's name", "RenewSubscriptionRequest:OwnerPhoneNumber": "Subscriber's phone number", "RenewSubscriptionRequest:OwnerEmail": "Subscriber's email", "RenewSubscriptionRequest:DiscountRate": "Employee Discount Rate", "Damascus": "Damascus", "Aleppo": "Aleppo", "Raqqa": "<PERSON><PERSON><PERSON>", "AsSuwayda": "AsSuwayda", "Daraa": "<PERSON><PERSON>", "DeirezZor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hama": "<PERSON><PERSON>", "AlHasakah": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Homs": "Homs", "Idlib": "<PERSON><PERSON><PERSON>", "Latakia": "Latakia", "Quneitra": "Quneitra", "RifDimashq": "RifDimashq", "Tartus": "<PERSON><PERSON><PERSON>", "Active": "Active", "Deactive": "Deactive", "stage": "Stage", "Request:Install:Devices": "Install Devices", "Request:Start:Processing": "Start Processing", "Request:Finish:Processing": "Finish Processing", "Request:Reject": "Reject Request", "Request:Note": "Note", "Request:RejectReason": "Rejection Reason", "Request:TrackVehicles": "Track Vehicles", "Request:Notes": "Notes", "VehicleInfo:ColorHex": "Vehicle Color", "VehicleInfo:LicensePlateSubClass": "License Plate Branch", "VehicleInfo:LicensePlateSerial": "License Plate Number", "ThisVehicleOrVehicleGroupIsNotAssociatedToThisObserver": "This vehicle or vehicle group is not associated with this observer", "Request:PersonalAccountSubscription": "Personal Account Subscription", "Request:BusinessAccountSubscription": "Business Account Subscription", "View": "View", "Update": "Update", "Done": "Done", "Create": "Create", "Print": "Print", "Export To PDF": "Export To PDF", "Export To EXCEL": "Export To EXCEL", "Toggle All": "Toggle All", "ITEMS_PER_PAGE_LABEL": "Items per page", "RANGE_PAGE_LABEL_2": "Toggle pages", "CreationTime": "Creation Time", "creationTime": "Creation Time", "type": "Type", "ownerPhoneNumber": "Owner Phone Number", "ownerName": "Owner Name", "price": "Price", "observerCount": "Observer Count", "subscriptionPlan": "Subscription Plan", "accountSubscriptionRequestStage": "Account Subscription Request Stage", "trackerInstallationLocation": "Tracker Installation Location", "Request:CompanyName": "Company Name", "Request:CompanyAddress": "Company Address", "note": "Note", "deviceId": "Tracking Device", "date": "Date", "status": "Status", "model": "Model", "brand": "Brand", "imei": "IMEI", "sim": "SIM", "protocolName": "Protocol", "colorHex": "Vehicle Color", "licensePlateSubClass": "License Plate Branch", "licensePlateSerial": "License Plate Number", "Devices": "Tracking Devices", "Unconnected": "Unconnected", "ConnectedAndActive": "Connected and Active", "ConnectedAndDeactive": "Connected and Deactive", "GoTrack:DeviceCountCannotBeGreaterThanVehicleCount": "Device count cannot be greater than vehicle count.", "rejectReason": "Reject Reason", "GoTrack:SmsBundleRenewalStage": "Stage", "GoTrack:SmsBundlePrice": "Price", "GoTrack:SmsBundleName": "Bundle Name", "RenewSMSPackageRequest:CreationTime": "Application submission date", "RenewSMSPackageRequest:OwnerFirstName": "Subscriber's name", "RenewSMSPackageRequest:OwnerPhoneNumber": "Subscriber's phone number", "RenewSMSPackageRequest:OwnerEmail": "Subscriber's email", "RenewSMSPackageRequest:TrackAccountName": "Tracking account name", "RenewSMSPackageRequest:TrackAccountSubscriptionFrom": "Subscription start date", "RenewSMSPackageRequest:TrackAccountSubscriptionTo": "Subscription end date", "RenewSMSPackageRequest:Status": "Request status", "RenewSMSPackageRequest:SmsBundleRenewalStage": "Request stage", "GoTrack:AddVehiclesRequest": "Add Vehicles Requests", "GoTrack:AddVehiclesRequestDetails": "Add Vehicles Request Details", "RequestsManagement": "Requests Management", "AddVehiclesRequest:AddVehiclesRequestStage": "Stage", "AddVehiclesRequest:TrackVehiclesCount": "Track Vehicles Count", "AddVehiclesRequest:AddVehicles": "Added Vehicles", "AddVehiclesRequest:OwnerFirstName": "Subscriber's name", "AddVehiclesRequest:OwnerPhoneNumber": "Subscriber's phone number", "AddVehiclesRequest:OwnerEmail": "Subscriber's email", "AddVehiclesRequest:TrackAccountName": "Tracking account name", "AddVehiclesRequest:TrackAccountSubscriptionFrom": "Subscription start date", "AddVehiclesRequest:TrackAccountSubscriptionTo": "Subscription end date", "AddVehiclesRequest:CreationTime": "Application submission date", "CashPayment": "Cash Payment", "ErrorOperation": "Error Operation", "Enum:SubscriptionPlan.0": "Silver", "Enum:SubscriptionPlan.1": "Gold", "Enum:SubscriptionPlan.2": "Platinum", "GoTrack:PricingAndBillingItemsManagement": "Pricing And Billing Items Management", "PricingManagement": "Pricing Management", "PricingItems": "Pricing Items", "displayName": "Item Name", "currentPrice": "Current Price", "GoTrack:SmsBundlePricingManagement": "SMS Bundle Pricing Management", "SmsBundlePricing": "SMS Bundle Pricing", "SuccessDelete": "Deleted Successfully", "AreYouSureYouWantToDelete?": "Are you sure you want to delete?", "UnexpectedError": "Unexpected Error", "UpdatedSuccessfully": "Updated Successfully", "Warning": "Warning", "AddNewSmsBundle": "Add New SMS Bundle", "UpdateSmsBundle": "Update SMS Bundle", "messagesCount": "Number of SMS messages", "durationInMonth": "Subscription duration (in months)", "isPercentage": "Percentage value (Yes/No)", "startDate": "Activation start date", "endDate": "Activation end date", "GoTrack:SubscriptionDurationDiscountManagement": "Subscription Duration Discount Management", "AddSubscriptionDurationDiscount": "Add Subscription Duration Discount", "NoRequestsSelected": "No Requests Selected", "SuccessfulRejection": "Rejected Successfully", "RenewSubscriptionRequest:NewSubscriptionPlan": "The new subscription plan", "GoTrack:PromoCodeManagement": "Promo Code Management", "code": "Promo Code", "discountValue": "Discount Value", "discountValue:Hint": "Discount percentage between 0 and 1", "isCurrentlyActive": "Active", "usageCount": "Usage Count", "dateFrom": "Validity Start Date", "dateTo": "Validity End Date", "PromoCode:Deactivate": "Deactivate", "PromoCode:UpdateRange": "Edit Validity Period", "PromoCode:Add": "Add Promo Code", "newStartDate": "New Validity Start Date", "newEndtDate": "New Validity End Date", "PromoCode:Warning.DiscountStartDateMustBeTodayOrBeforeEndDate": "The validity start date must be today or later, and before the validity end date.", "PromoCode:Warning.DiscountValueOutOfRange": "The discount value must be between 0 and 1", "SMSBundleExpiredMessage": "Your SMS bundle has expired.", "SMSBundleExpiredTitle": "SMS Bundle Expired", "SMSBundleLowCountMessage": "Your sms message package is about to expire.", "SMSBundleLowCountTitle": "The sms bundle is about to expire."}}