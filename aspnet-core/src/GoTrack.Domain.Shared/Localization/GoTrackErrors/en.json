{"culture": "en", "texts": {"GoTrack:Error.DuplicateLicensePlate": "Vehicle plate information already exists", "GoTrack:Error.DuplicateGeoZoneName": "Geo Zone name already exists", "GoTrack:Error.DuplicateGeoZonePolyLine": "Geo Zone poly line already exists", "GoTrack:Error.MustHaveTwoCoordinatesAtLeast": "Must have two coordinates at least", "GoTrack:Error.MustHaveThreeCoordinatesAtLeast": "Must have three coordinates at least", "GoTrack:Error.InValidPolylineAlgorithm": "Invalid Polyline <PERSON>", "GoTrack:Error.OneOrMoreVehiclesNotFound": "One or more vehicles not found", "GoTrack:Error.OneOrMoreVehiclesDoNotHaveADevice": "One or more vehicles do not have a device", "GoTrack:Error.NoInstalledDeviceFoundForVehicle": "No installed device found for the vehicle", "GoTrack:Error.DuplicateDevice": "There is one or more duplicate devices", "GoTrack:Error.TheDeviceStatusMustBeInoperative": "The status of the entered device must be Inoperative", "GoTrack:Error.TheDeviceStatusWithImeiMustBeInoperative": "The status of the entered device with imei {0} must be Inoperative", "GoTrack:Error.DuplicateEntriesFoundForLicensePlateSerialAndSubClass": "A duplicate entry exists for the subclass and license plate serial number", "GoTrack:Error.TheNumberOfVehicleAndDeviceDoesNotMatchTheNumberOfRequest": "The number of entered vehicles and devices does not match the subscription request", "GoTrack:Error.SomeLicensePlateDoNotMatchWithRequest": "Some license plates do not match the request", "GoTrack:Error.DeviceIsAlreadyInstalled": "Device is already installed", "GoTrack:Error.DeviceIsAlreadyUninstalled": "Device is already uninstalled", "GoTrack:Error.InitialStatusOfAssigningDeviceToVehicleMustBeInstalled": "The initial status of assigning a device to a vehicle to must be installed", "GoTrack:Error.BothImeiAndSimDuplicated": "Both the IMEI and SIM are already in use", "GoTrack:Error.TheImeiIsDuplicated": "The IMEI is already in use", "GoTrack:Error.TheSimIsDuplicated": "The SIM is already in use", "GoTrack:Error.MsisdnInvalidFormat": "Invalid Msisdn format", "GoTrack:Error.OtpInvalid": "Invalid OTP", "GoTrack:Error.InvalidImageFormat": "Invalid image format", "GoTrack:Error.InvalidImageSize": "Invalid image size", "GoTrack:Error.AddressEnteredIsInvalid": "Invalid address entered", "GoTrack:Error.SubscriptionInvalidDateRange": "Subscription invalid date range", "GoTrack:Error.SubscriptionInvalidVehicles": "Subscription invalid vehicles", "GoTrack:Error.InvalidRequestStatusOnlyPendingCanCanceled": "Invalid request status only pending can canceled", "GoTrack:Error.TrackAccountIdNotFound": "Track account id not found", "GoTrack:Error.VehicleNotInGroup": "Vehicle not in group", "GoTrack:Error.VehicleAlreadyExistsInThisGroup": "Vehicle already exists in this group", "GoTrack:Error.ThisVehicleOrVehicleGroupIsNotAssociatedToThisObserver": "This vehicle or vehicle group is not Associated to this observer", "GoTrack:Error.NoAccess": "You do not have access.", "GoTrack:Error.OneOrMoreObserversNotFound": "One or more observers not found", "GoTrack:Error.ObserverWithVehicleOrVehicleGroupAlreadyExists": "Observer with this vehicle or this vehilce group already exists", "GoTrack:Error.WrongWayToAddOwnerToTrackAccount": "Wrong way to add owner to track account", "GoTrack:Error.AlertTypeIsInvalid": "Invalid ALert Type", "GoTrack:Error.AlertOnVehicleOrVehicleGroupAlreadyExists": "Alert with this vehicle or this vehicle group already exists", "GoTrack:Error.RouteDoesNotContainStopPoint": "Route doesn't contain this stop point", "GoTrack:Error.TripTemplateWithVehicleOrVehicleGroupAlreadyExists": "Trip template with vehicle or vehicle group already exists", "GoTrack:Error.PriceCannotBeNegative": "The price cannot be negative", "GoTrack:Error.InvalidSubscriptionStage": "The subscription stage is invalid", "GoTrack:Error.ValueIsAlreadySet": "Value is already set", "GoTrack:Error.ObserverCountTooLow": "The observer count ({0}) is below the minimum required ({MinimumRequired}).", "GoTrack:Error.ObserverLimitReached": "The maximum number of observers ({MaxObservers}) has been reached.", "GoTrack:Error.FcmTokenMustBeUnique": "FcmToken must be unique", "GoTrack:Error.OwnerIDHasAlreadyBeenSet": "The owner ID has already been set", "GoTrack:Error.DeviceStatusIsAlreadyActive": "Device status is already active", "GoTrack:Error.DeviceStatusIsAlreadyDeactive": "Device status is already deactive", "GoTrack:Error.DeviceAlreadyOwned": "This device is already assigned to another owner. Please select an unassigned device.", "GoTrack:Error.DeviceWithImeiAlreadyOwned": "Device with imei {0} is already assigned to another owner. Please select an unassigned device.", "GoTrack:Error.DeviceStatusInvalidForNonTracking": "The device status is invalid for a vehicle that doesn't need tracking. Device must be in 'Connected and Deactive' status.", "GoTrack:Error.DeviceStatusWithImeiInvalidForNonTracking": "The status of device with imei {0} is invalid for a vehicle that doesn't need tracking. Devi<PERSON> must be in 'Connected and Deactive' status.", "GoTrack:Error.TrackingDeviceOwnerMismatch": "The tracking device owner does not match the request owner.", "GoTrack:Error.TrackingDeviceWithImeiOwnerMismatch": "Owner of The tracking device with imei {0} does not match the request owner.", "GoTrack:Error.DeviceCannotBeDeactivated": "The device cannot be deactivated because it is not currently active.", "GoTrack:Error.DeviceCannotBeActivated": "The device cannot be activated because it is not in an unconnected state.", "GoTrack:Error.InvalidUserCountRequestStage": "The request cannot be marked as completed because it is not in the 'Payment' stage", "GoTrack:Error.InactiveSubscription": "The subscription is inactive", "GoTrack:Error.DuplicateUserCountRequest": "A user count request already exists", "GoTrack:Error.InvalidUserCount": "Invalid User Count", "GoTrack:Error.CannotApproveBill": "The bill can only be approved if it is pending approval.", "GoTrack:Error.CannotRejectBill": "The bill can only be rejected if it is pending approval.", "GoTrack:Error.CannotMarkBillAsPaid": "The bill can only be marked as paid if it is approved.", "GoTrack:Error.CannotAddBillLineItem": "Bill line items can only be added when the bill is in draft or pending approval.", "GoTrack:Error.CannotApplyDiscountToBill": "Discounts can only be applied when the bill is in draft or pending approval.", "GoTrack:Error.DiscountInvalidDateRange": "The start date must be before the end date.", "GoTrack:Error.DiscountRequestIdRequired": "The request id is required for this discount type.", "GoTrack:Error.DiscountPricingItemKeyRequired": "The pricing item key is required for this discount type.", "GoTrack:Error.DiscountDurationInMonthRequired": "The duration in months is required for this discount type.", "GoTrack:Error.DiscountInvalidEndDate": "The end date must be in the future.", "GoTrack:Error.SubscriptionRequestUnderReview": "A renew subscription request is already under review", "GoTrack:Error.TrackerInstallationLocationRequired": "Tracker installation location is required when adding new vehicles", "GoTrack:Error.UserRemovalListRequired": "User removal list must be provided when reducing user count", "GoTrack:Error.DeviceRequestsRequired": "At least one device request must be provided", "GoTrack:Error.NewVehiclesRequired": "Tracker installation location is provided, but no new vehicles are available", "GoTrack:Error.SubscriptionStillPending": "The subscription is still in a pending state", "GoTrack:Error.SubscriptionAlreadyActive": "The subscription is already active", "GoTrack:Error.DeviceRequiredForActivation": "Device is required for activation", "GoTrack:Error.InvalidSubscriptionStateForActivation": "The subscription state is invalid for activation", "GoTrack:Error.DiscountPricingItemNotFound": "The pricing item was not found.", "GoTrack:Error.InvalidRenewalStage": "The SMS bundle renewal request is not in the 'Payment' stage", "GoTrack:Error.DuplicateSmsBundleRenewalRequest": "A renewal request for this subscription is already exists", "GoTrack:Error.CannotActivateSubscription": "The subscription can only be activated from the pending state.", "GoTrack:Error.PaymentNotAllowed": "Payment not allowed", "GoTrack:Error.RequestDoesNotSupportPayments": "Request does not support payments", "GoTrack:Error.CannotSetBillAsPendingApproval": "<PERSON><PERSON> set bill status to pending approval.", "GoTrack:Error.BillAlreadyAssigned": "The bill is already assigned to this request.", "GoTrack:Error.BillNotAssigned": "The bill is not assigned to this request.", "GoTrack:Error.DuplicateVehicle": "A vehicle with this license plate already exists.", "GoTrack:Error.InvalidRequestStatus": "Cannot process payment for this request in its current status.", "GoTrack:Error.ReversalPaymentFailed": "Failed to reverse the payment. Please try again or contact support.", "GoTrack:Error.DeviceTokenNotFound": "Device token was not found", "GoTrack:Error.InvalidLineString": "Invalid line", "GoTrack:Error.InvalidStopPoint": "Invalid stop point", "GoTrack:Error.ThisStopPointIsNotAssociatedToThisRoute": "This stop point is not associated to this route", "GoTrack:Error.InvalidColor": "Invalid color", "GoTrack:Error.OwnerObserverSameTrackAccount": "You can't be <PERSON> and Owner on the same Track Account", "GoTrack:Error.ObserverAlreadyExists": "Observer already exists", "GoTrack:Error.NumberNotSupported": "this number is not supported", "GoTrack:Error.DuplicateVehicleGroupName": "A vehicle group with this name already exists", "GoTrack:Error.InvalidDateRange": "Invalid date range specified.", "GoTrack:Error.InvalidMessagesCount": "Invalid messages count specified.", "GoTrack:Error.InvalidPrice": "Invalid price specified.", "GoTrack:Error.InvalidSubscriptionDuration": "Invalid subscription duration specified.", "GoTrack:Error.InvalidSmsBundlesCount": "Invalid SMS bundles count specified.", "GoTrack:Error.NotificationTimeMustBeAfterLastOne": "Notification time must be after the last one.", "GoTrack:Error.InitialStateCannotBeExpired": "Initial state cannot be expired.", "GoTrack:Error.CreatedTrackAccountAlreadySet": "Track account has already been created.", "GoTrack:Error.DeviceStatusIsAlreadyDeactivated": "Device status is already deactivated.", "GoTrack:Error.NoClientSecretCanBeSetForPublicApplications": "Client secret cannot be set for public applications.", "GoTrack:Error.TheClientSecretIsRequiredForConfidentialApplications": "Client secret is required for confidential applications.", "GoTrack:Error.InvalidRedirectUri": "Invalid redirect URI specified {0}", "GoTrack:Error.InvalidPostLogoutRedirectUri": "Invalid post-logout redirect URI specified {0}", "GoTrack:Error.RequestedMonthsCannotBeNull": "Requested months value cannot be null.", "GoTrack:Error.PriceNotSetFor": "Price is not set for {0}.", "GoTrack:Error.InvalidRequestType": "Invalid request type specified.", "GoTrack:Error.InvalidKey": "Invalid key specified.", "GoTrack:Error.DeviceCountCannotBeGreaterThanVehicleCount": "Device count cannot be greater than vehicle count.", "GoTrack:Error.RequestCanOnlyBeRejectedIfPending": "Only requests in pending status can be rejected.", "GoTrack:Error.RequestCanOnlyBeStartedIfPending": "Only requests in pending status can be started.", "GoTrack:Error.RequestCanOnlyBeFinishedIfProcessing": "Only requests in processing status can be finished.", "GoTrack:Error.EmailVerificationTokenExpired": "The verification link has expired. Please request a new one.", "GoTrack:Error.EmailVerificationEmailMismatch": "The email doesn't match the user's current email.", "GoTrack:Error.EmailVerificationAlreadyConfirmed": "This email address has already been confirmed.", "GoTrack:Error.DiscountSpecificationDurationInvalid": "The duration for discount specification must be a valid number between 1 and 12 months.", "GoTrack:Error.DiscountSpecificationPricingItemInvalid": "The specified pricing item key is invalid or not found.", "GoTrack:Error.DiscountSpecificationRequestTypeInvalid": "The specified request type is invalid.", "GoTrack:Error.DiscountSpecificationUserCountInvalid": "The user count must be a valid number greater than zero.", "GoTrack:Error.DiscountPercentageValueOutOfRange": "The discount percentage value must be between 0 and 1.", "GoTrack:Error.DiscountSpecificationNotFound": "The discount specification was not found.", "GoTrack:Error.DiscountPricingItemKeysRequired": "You must provide at least one pricing item key for item-based discounts.", "GoTrack:Error.DiscountSpecificationRequestIdInvalid": "The specified request id is invalid.", "GoTrack:Error.RequestIdRequired": "Request ID is required.", "GoTrack:Error.OwnerIdRequired": "Owner ID is required.", "GoTrack:Error.SubscriptionPlanRequired": "Subscription plan is required.", "GoTrack:Error.DevicesCountNegative": "Devices count cannot be negative.", "GoTrack:Error.TrackVehiclesCountNegative": "Track vehicles count cannot be negative.", "GoTrack:Error.DevicesCountExceedsVehiclesCount": "Devices count cannot exceed vehicles count.", "GoTrack:Error.VehicleCountNegative": "Vehicle count cannot be negative.", "GoTrack:Error.RemainingMonthsInvalid": "Remaining months must be greater than zero.", "GoTrack:Error.SmsBundleIdRequired": "SMS bundle ID is required.", "GoTrack:Error.UserAlreadyHasPersonalSubscription": "User already has a personal subscription", "GoTrack:Error.UserAlreadyHasBusinessSubscription": "User already has a business subscription", "GoTrack:Error.MaxRequestsExceeded": "Maximum number of requests exceeded.", "GoTrack:Error.PromoCodeRequired": "Promo code is required", "GoTrack:Error.PromoCodeInvalidLength": "Promo code must be between 4 and 8 characters", "GoTrack:Error.PromoCodeAlreadyExists": "An active promo code with this code already exists", "GoTrack:Error.PromoCodeNotFound": "Promo code not found", "GoTrack:Error.PromoCodeNotActive": "This promo code is not active", "GoTrack:Error.PromoCodeExpired": "This promo code has expired", "GoTrack:Error.PromoCodeEndDateMustBeInFuture": "The promo code end date must be in the future", "GoTrack:Error.PromoCodeStartDateMustBeInFuture": "The promo code start date must be in the future", "GoTrack:Error.PromoCodeContainsWhiteSpace": "Promo code cannot contain whitespace", "GoTrack:Error.DiscountFixedValueMustBePositive": "The fixed discount value must be greater than zero.", "GoTrack:Error.GracePeriodMustBeNonNegative": "Grace period must be a greater than or equal zero", "GoTrack:Error.NotificationDaysMustBePositive": "Each notification day must be a positive number.", "GoTrack:Error.NoSmsBundleOrFinishedOfTrackAccountSubscription": "No SMS bundle available or the existing bundle has expired for tracking account subscription {0}.", "GoTrack:Error.SmsLowCountThresholdMustBePositive": "SMS low count threshold must be a positive number."}}