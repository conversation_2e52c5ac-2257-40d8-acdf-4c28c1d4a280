{"culture": "ar", "texts": {"GoTrack:Error.DuplicateLicensePlate": "معلومات لوحة المركبة موجودة مسبقا", "GoTrack:Error.DuplicateGeoZoneName": "اسم المنطقة الجغرافية موجود مسبقا", "GoTrack:Error.DuplicateGeoZonePolyLine": "احداثيات المنطقة الجغرافية موجود مسبقا", "GoTrack:Error.MustHaveTwoCoordinatesAtLeast": "يج<PERSON> أن يكون لديك إحداثيتين على الأقل", "GoTrack:Error.MustHaveThreeCoordinatesAtLeast": "يج<PERSON> أن يكون لديك ثلاثة إحداثيات على الأقل", "GoTrack:Error.InValidPolylineAlgorithm": "خوارزمية مسار الاحداثيات ليست صحيحة", "GoTrack:Error.OneOrMoreVehiclesNotFound": "لم يتم العثور على إحدى المركبات", "GoTrack:Error.OneOrMoreVehiclesDoNotHaveADevice": "إحدى المركبات لا تحتوي على جهاز", "GoTrack:Error.NoInstalledDeviceFoundForVehicle": "لم يتم العثور على جهاز مثبت للمركبة", "GoTrack:Error.DuplicateDevice": "يو<PERSON>د جهاز مكرر أو أكثر", "GoTrack:Error.TheDeviceStatusMustBeInoperative": "يجب أن تكون حالة الجهاز المدخل غير فعال", "GoTrack:Error.TheDeviceStatusWithImeiMustBeInoperative": "يجب أن تكون حالة الجهاز ذات المعرف {0} المدخل غير فعال", "GoTrack:Error.DuplicateEntriesFoundForLicensePlateSerialAndSubClass": "يو<PERSON><PERSON> إدخا<PERSON> مكرر للفئة الفرعية والرقم التسلسي للوحة الترخيص", "GoTrack:Error.TheNumberOfVehicleAndDeviceDoesNotMatchTheNumberOfRequest": "عد<PERSON> الاجهزة والمركبات المدخلة غير متطابق مع طلب اشتراك", "GoTrack:Error.SomeLicensePlateDoNotMatchWithRequest": "بعض لوحات الترخيص لا تتطابق مع الطلب", "GoTrack:Error.DeviceIsAlreadyInstalled": "تم تثبيت الجهاز سابقا", "GoTrack:Error.DeviceIsAlreadyUninstalled": "تم الغاء تثبيت الجهاز سابقا", "GoTrack:Error.InitialStatusOfAssigningDeviceToVehicleMustBeInstalled": "يجب أن تكون الحالة الابتدائية عند ربط جهاز بمركبة تثبيت", "GoTrack:Error.BothImeiAndSimDuplicated": "رمز الجهاز و شريحة الاتصال مستخدمان بالفعل", "GoTrack:Error.TheImeiIsDuplicated": "رمز الجهاز مستخدم بالفعل", "GoTrack:Error.TheSimIsDuplicated": "شريحة الاتصال مستخدمة بالفعل", "GoTrack:Error.MsisdnInvalidFormat": "صيغة رقم الموبايل غير صالحة", "GoTrack:Error.OtpInvalid": "رمز التحقق غير صحيح", "GoTrack:Error.InvalidImageFormat": "صيغة الصورة غير صحيحة", "GoTrack:Error.InvalidImageSize": "حجم الصورة غير صحيح", "GoTrack:Error.AddressEnteredIsInvalid": "العنوان المدخل غير صحيح", "GoTrack:Error.SubscriptionInvalidDateRange": "نظاق التاريخ غير صحيح", "GoTrack:Error.SubscriptionInvalidVehicles": "معلومات المركبات غير صحيحة", "GoTrack:Error.InvalidRequestStatusOnlyPendingCanCanceled": "حالة الطلب غير صالحة فقط قيد الانتظار يمكن إلغاؤها", "GoTrack:Error.TrackAccountIdNotFound": "معرف حسا<PERSON> المسار غير مجود", "GoTrack:Error.VehicleNotInGroup": "المركبة غير موجودة بالمجموعة", "GoTrack:Error.VehicleAlreadyExistsInThisGroup": "المركبة موجودة مسبقا في هذه المجموعة", "GoTrack:Error.ThisVehicleOrVehicleGroupIsNotAssociatedToThisObserver": "هذه المركبة او اسطول المركبات غير مرتبط مع هذا المراقب", "GoTrack:Error.NoAccess": "ليس لديك اية امكانية للوصول", "GoTrack:Error.OneOrMoreObserversNotFound": "لم يتم العثور على احد المراقبين", "GoTrack:Error.ObserverWithVehicleOrVehicleGroupAlreadyExists": "المراقب مع هذه السيارة او هذه المجموعة موجود مسبقا", "GoTrack:Error.WrongWayToAddOwnerToTrackAccount": "تتبع طريقة خاطئة لاضافة مالك للحساب", "GoTrack:Error.AlertTypeIsInvalid": "نوع التنبيه غير صحيح", "GoTrack:Error.AlertOnVehicleOrVehicleGroupAlreadyExists": "هذا التنبيه مع هذه السيارة او هذه المجموعة موجود مسبقا", "GoTrack:Error.RouteDoesNotContainStopPoint": "النقطة لا تنتمي للمسار", "GoTrack:Error.TripTemplateWithVehicleOrVehicleGroupAlreadyExists": "قالب الجولة يحوي هذه السيارة او هذه المجموعة من السيارات", "GoTrack:Error.PriceCannotBeNegative": "لا يمكن أن يكون السعر سالبًا", "GoTrack:Error.InvalidSubscriptionStage": "مرحلة الاشتراك غير صالحة", "GoTrack:Error.ValueIsAlreadySet": "القيمة موجودة مسبقا", "GoTrack:Error.ObserverCountTooLow": "عدد المراقبين ({0}) أقل من الحد الأدنى المطلوب ({MinimumRequired}).", "GoTrack:Error.ObserverLimitReached": "تم الوصول إلى الحد الأقصى لعدد المراقبين ({MaxObservers}).", "GoTrack:Error.FcmTokenMustBeUnique": " FcmToken يجب أن تكون فريدة", "GoTrack:Error.OwnerIDHasAlreadyBeenSet": "تم تعيين معرف المالك مسبقًا", "GoTrack:Error.DeviceStatusIsAlreadyActive": "ان الجهاز حاليا فعال", "GoTrack:Error.DeviceStatusIsAlreadyDeactive": "ان الجهاز حاليا غير فعال", "GoTrack:Error.DeviceAlreadyOwned": "هذا الجهاز مخصص بالفعل لمالك آخر. الرجاء اختيار جهاز غير مخصص.", "GoTrack:Error.DeviceWithImeiAlreadyOwned": "الجهاز ذات المعرف {0} مخصص بالفعل لمالك آخر. الرجاء اختيار جهاز غير مخصص.", "GoTrack:Error.DeviceStatusInvalidForNonTracking": "حالة الجهاز غير صالحة لمركبة لا تحتاج إلى تعقب. يجب أن يكون الجهاز في حالة 'متصل وغير نشط'.", "GoTrack:Error.DeviceStatusWithImeiInvalidForNonTracking": "حالة الجهاز ذات المعرف {0} غير صالحة لمركبة لا تحتاج إلى تعقب. يجب أن يكون الجهاز في حالة 'متصل وغير نشط'.", "GoTrack:Error.TrackingDeviceOwnerMismatch": "مالك جهاز التتبع لا يتطابق مع مالك الطلب.", "GoTrack:Error.TrackingDeviceWithImeiOwnerMismatch": "مالك جهاز ذات المعرف {0} التتبع لا يتطابق مع مالك الطلب.", "GoTrack:Error.DeviceCannotBeDeactivated": "لا يمكن إلغاء تنشيط الجهاز لأنه غير نشط حاليًا.", "GoTrack:Error.DeviceCannotBeActivated": "لا يمكن تنشيط الجهاز لأنه ليس في حالة غير متصلة.", "GoTrack:Error.InvalidUserCountRequestStage": "لا يمكن جعل اكمال الطلب لأنه ليس في مرحلة الدفع", "GoTrack:Error.InactiveSubscription": "الاشتراك غير فعال", "GoTrack:Error.DuplicateUserCountRequest": "طلب زيادة عدد المستخدمين موجود بالفعل", "GoTrack:Error.InvalidUserCount": "عدد المستخدمين غير صحيح", "GoTrack:Error.CannotApproveBill": "يمكن الموافقة على الفاتورة فقط إذا كانت في انتظار الموافقة.", "GoTrack:Error.CannotRejectBill": "يمكن رفض الفاتورة فقط إذا كانت في انتظار الموافقة.", "GoTrack:Error.CannotMarkBillAsPaid": "يمكن وضع علامة على الفاتورة كمسددة فقط إذا كانت معتمدة.", "GoTrack:Error.CannotAddBillLineItem": "يمكن إضافة عناصر الفاتورة فقط عندما تكون الفاتورة في حالة مسودة أو في انتظار الموافقة.", "GoTrack:Error.CannotApplyDiscountToBill": "يمكن تطبيق الخصومات فقط عندما تكون الفاتورة في حالة مسودة أو في انتظار الموافقة.", "GoTrack:Error.DiscountInvalidDateRange": "يجب أن يكون تاريخ البداية قبل تاريخ النهاية.", "GoTrack:Error.DiscountRequestIdRequired": "معرف الطلب مطلوب لنوع الحسم هذا.", "GoTrack:Error.DiscountPricingItemKeyRequired": "مف<PERSON><PERSON><PERSON> التسعير مطلوب لنوع الحسم هذا", "GoTrack:Error.DiscountDurationInMonthRequired": "المدة بالأشهر مطلوبة لنوع الحسم هذا.", "GoTrack:Error.DiscountInvalidEndDate": "يجب أن يكون تاريخ النهاية في المستقبل.", "GoTrack:Error.SubscriptionRequestUnderReview": "يوجد طلب تجديداشتراك قيد المراجعة", "GoTrack:Error.TrackerInstallationLocationRequired": "مطلوب تحديد موقع تثبيت جهاز التتبع عند إضافة مركبات جديدة", "GoTrack:Error.UserRemovalListRequired": "يجب تقديم قائمة بالمستخدمين المطلوب إزالتهم عند تقليل عدد المستخدمين", "GoTrack:Error.DeviceRequestsRequired": "يجب توفير جهاز وا<PERSON>د على الأقل ليتم تثبيته", "GoTrack:Error.NewVehiclesRequired": "تم تحديد موقع تركيب جهاز التتبع، لكن لا توجد مركبات جديدة لتركيب جهاز لها", "GoTrack:Error.SubscriptionStillPending": "الاشتراك لا يزال في حالة انتظار", "GoTrack:Error.SubscriptionAlreadyActive": "الاشتراك مفعل بالفعل", "GoTrack:Error.DeviceRequiredForActivation": "الجهاز مطلوب لتفعيل الاشتراك", "GoTrack:Error.InvalidSubscriptionStateForActivation": "حالة الاشتراك غير صالحة للتفعيل", "GoTrack:Error.DiscountPricingItemNotFound": "لم يتم العثور على عنصر التسعير", "GoTrack:Error.InvalidRenewalStage": "طلب تجديد باقة الرسائل القصيرة ليس في مرحلةالدفع", "GoTrack:Error.DuplicateSmsBundleRenewalRequest": "هناك طلب تجديد باقة رسائل قصيرة موجود بالفعل", "GoTrack:Error.CannotActivateSubscription": "يمكن تنشيط الاشتراك فقط من الحالة المعلقة.", "GoTrack:Error.PaymentNotAllowed": "عملية الدفع ممنوعة", "GoTrack:Error.RequestDoesNotSupportPayments": "الطلب لا يدعم عمليات الدفع", "GoTrack:Error.CannotSetBillAsPendingApproval": "لا يمكن تغيير حالة الفاتورة إلى قيد الموافقة.", "GoTrack:Error.BillAlreadyAssigned": "تم تعيين الفاتورة بالفعل لهذا الطلب.", "GoTrack:Error.BillNotAssigned": "لم يتم تعيين فاتورة لهذا الطلب.", "GoTrack:Error.DuplicateVehicle": "مركبة بهذه اللوحة موجودة بالفعل", "GoTrack:Error.InvalidRequestStatus": "لا يمكن معالجة الدفع لهذا الطلب في حالته الحالية", "GoTrack:Error.ReversalPaymentFailed": "فشل في عكس الدفع. يرجى المحاولة مرة أخرى أو الاتصال بالدعم", "GoTrack:Error.DeviceTokenNotFound": "ان DeviceToken غير موجود", "GoTrack:Error.InvalidLineString": "الخط غير صحيح", "GoTrack:Error.InvalidStopPoint": "نقطة التوقف غير صحيحة", "GoTrack:Error.ThisStopPointIsNotAssociatedToThisRoute": "نقطة التوقف هذه غير مرتبطه بهذا المسار", "GoTrack:Error.InvalidColor": "اللون غير صحيح", "GoTrack:Error.OwnerObserverSameTrackAccount": "لا يمكنك ان تكون مراقب ومالك لهذا الحساب", "GoTrack:Error.ObserverAlreadyExists": "معلومات المراقب موجودة مسبفا", "GoTrack:Error.NumberNotSupported": "هذا الرقم غير مدعوم", "GoTrack:Error.DuplicateVehicleGroupName": "مجموعة مركبات بهذا الاسم موجودة بالفعل", "GoTrack:Error.InvalidDateRange": "نطاق التاريخ المحدد غير صالح.", "GoTrack:Error.InvalidMessagesCount": "عدد الرسائل المحدد غير صالح.", "GoTrack:Error.InvalidPrice": "السعر المحدد غير صالح.", "GoTrack:Error.InvalidSubscriptionDuration": "مدة الاشتراك المحددة غير صالحة.", "GoTrack:Error.InvalidSmsBundlesCount": "عدد حزم الرسائل القصيرة المحدد غير صالح.", "GoTrack:Error.NotificationTimeMustBeAfterLastOne": "يجب أن يكون وقت الإشعار بعد الإشعار السابق.", "GoTrack:Error.InitialStateCannotBeExpired": "لا يمكن أن تكون الحالة الأولية منتهية الصلاحية.", "GoTrack:Error.CreatedTrackAccountAlreadySet": "تم إنشاء حساب التتبع بالفعل.", "GoTrack:Error.DeviceStatusIsAlreadyDeactivated": "حالة الجهاز معطلة بالفعل.", "GoTrack:Error.NoClientSecretCanBeSetForPublicApplications": "لا يمكن تعيين سر العميل للتطبيقات العامة.", "GoTrack:Error.TheClientSecretIsRequiredForConfidentialApplications": "سر العميل مطلوب للتطبيقات السرية.", "GoTrack:Error.InvalidRedirectUri": "رابط إعادة التوجيه المحدد غير صالح {0}", "GoTrack:Error.InvalidPostLogoutRedirectUri": "رابط إعادة التوجيه بعد تسجيل الخروج غير صالح {0}", "GoTrack:Error.RequestedMonthsCannotBeNull": "قيمة الأشهر المطلوبة لا يمكن أن تكون فارغة.", "GoTrack:Error.PriceNotSetFor": "السعر غير مضبوط لـ {0}.", "GoTrack:Error.InvalidRequestType": "نوع الطلب المحدد غير صالح.", "GoTrack:Error.InvalidKey": "المفتاح المحدد غير صالح.", "GoTrack:Error.DeviceCountCannotBeGreaterThanVehicleCount": "عدد الأجهزة لا يمكن أن يكون أكبر من عدد المركبات.", "GoTrack:Error.RequestCanOnlyBeRejectedIfPending": "يمكن رفض الطلبات التي في حالة الانتظار فقط.", "GoTrack:Error.RequestCanOnlyBeStartedIfPending": "يمكن بدء معالجة الطلبات التي في حالة الانتظار فقط.", "GoTrack:Error.RequestCanOnlyBeFinishedIfProcessing": "يمكن إنهاء الطلبات التي في حالة المعالجة فقط.", "GoTrack:Error.EmailVerificationTokenExpired": "انتهت صلاحية رابط التحقق. يرجى طلب رابط جديد", "GoTrack:Error.EmailVerificationEmailMismatch": "البريد الإلكتروني لا يتطابق مع البريد الحالي للمستخدم", "GoTrack:Error.EmailVerificationAlreadyConfirmed": "تم تأكيد عنوان البريد الإلكتروني هذا مسبقًا", "GoTrack:Error.DiscountSpecificationDurationInvalid": "مدة الحسم يجب أن تكون رقماً صالحاً بين 1 و 12 شهراً.", "GoTrack:Error.DiscountSpecificationPricingItemInvalid": "مفتاح عنصر التسعير المحدد غير صالح أو غير موجود.", "GoTrack:Error.DiscountSpecificationRequestTypeInvalid": "نوع الطلب المحدد غير صالح.", "GoTrack:Error.DiscountSpecificationUserCountInvalid": "عدد المستخدمين يجب أن يكون رقماً صحيحاً أكبر من صفر.", "GoTrack:Error.DiscountPercentageValueOutOfRange": "قيمة نسبة الحسم يجب أن تكون بين 0 و 1.", "GoTrack:Error.DiscountSpecificationNotFound": "هذا الشرط غير معرّف", "GoTrack:Error.DiscountPricingItemKeysRequired": "يجب توفير مفتاح عنصر تسعير واح<PERSON> على الأقل للخصومات المرتبطة بالعناصر.", "GoTrack:Error.DiscountSpecificationRequestIdInvalid": "معرّف الطلب المحدد غير صالح.", "GoTrack:Error.RequestIdRequired": "معرف الطلب مطلوب.", "GoTrack:Error.OwnerIdRequired": "معرف المالك مطلوب.", "GoTrack:Error.SubscriptionPlanRequired": "خطة الاشتراك مطلوبة.", "GoTrack:Error.DevicesCountNegative": "لا يمكن أن يكون عدد الأجهزة سالبًا.", "GoTrack:Error.TrackVehiclesCountNegative": "لا يمكن أن يكون عدد مركبات التتبع سالبًا.", "GoTrack:Error.DevicesCountExceedsVehiclesCount": "لا يمكن أن يتجاوز عدد الأجهزة عدد المركبات.", "GoTrack:Error.VehicleCountNegative": "لا يمكن أن يكون عدد المركبات سالبًا.", "GoTrack:Error.RemainingMonthsInvalid": "يجب أن تكون الأشهر المتبقية أكبر من صفر.", "GoTrack:Error.SmsBundleIdRequired": "معرف حزمة الرسائل القصيرة مطلوب.", "GoTrack:Error.UserAlreadyHasPersonalSubscription": "المستخدم لديه اشتراك شخصي بالفعل", "GoTrack:Error.UserAlreadyHasBusinessSubscription": "المستخدم لديه اشتراك تجاري بالفعل", "GoTrack:Error.MaxRequestsExceeded": "لقد وصلت إلى الحد الأقصى لعدد الطلبات المسموح بها في هذا الوقت", "GoTrack:Error.PromoCodeRequired": "<PERSON><PERSON><PERSON> الحسم مطلوب", "GoTrack:Error.PromoCodeInvalidLength": "يجب أن يكون رمز الحسم بين 4 و 8 أحرف", "GoTrack:Error.PromoCodeAlreadyExists": "يو<PERSON><PERSON> رمز حسم نشط بهذا الرمز بالفعل", "GoTrack:Error.PromoCodeNotFound": "رمز الحسم غير موجود", "GoTrack:Error.PromoCodeNotActive": "رمز الحسم هذا غير نشط", "GoTrack:Error.PromoCodeExpired": "انتهت صلاحية رمز الحسم هذا", "GoTrack:Error.PromoCodeEndDateMustBeInFuture": "يجب أن يكون تاريخ انتهاء رمز الحسم في المستقبل", "GoTrack:Error.PromoCodeStartDateMustBeInFuture": "يجب أن يكون تاريخ بدء رمز الحسم في المستقبل", "GoTrack:Error.PromoCodeContainsWhiteSpace": "يجب أن لا يحتوي رمز الحسم على مسافات", "GoTrack:Error.DiscountFixedValueMustBePositive": "يجب أن تكون قيمة الحسم الثابت أكبر من صفر.", "GoTrack:Error.GracePeriodMustBeNonNegative": "يجب أن تكون فترة السماح رقماً أكبر أو يساوي الصفر", "GoTrack:Error.NotificationDaysMustBePositive": "يجب أن تكون كل يوم إشعار رقماً موجباً.", "GoTrack:Error.NoSmsBundleOrFinishedOfTrackAccountSubscription": "لا توجد حزمة رسائل نصية متاحة أو أن الحزمة الحالية منتهية الصلاحية لتتبع اشتراك الحساب {0}", "GoTrack:Error.SmsLowCountThresholdMustBePositive": "يجب أن يكون حد الرسائل النصية المنخفض رقماً موجباً."}}