{"culture": "en", "texts": {"PermissionsGroup:BusinessAccountSubscriptionRequestGroup": "Business account subscription requests management", "Permissions:BusinessAccountSubscriptionRequestDetails": "View Business account subscription request details", "Permissions:BusinessAccountSubscriptionRequestStartProcessing": "Start processing business account subscription request", "Permissions:BusinessAccountSubscriptionRequestFinishProcessing": "Finish processing business account subscription request", "Permissions:BusinessAccountSubscriptionRequestInstallDevices": "Install Devices for business account subscription request", "Permissions:PersonalAccountSubscriptionRequestInstallDevices": "Install Devices for Personal account subscription request ", "Permissions:BusinessAccountSubscriptionRequestReject": "Reject business account subscription request", "PermissionsGroup:PersonalAccountSubscriptionRequestGroup": "Personal account subscription requests management", "Permissions:PersonalAccountSubscriptionRequestDetails": "View personal account subscription request details", "Permissions:PersonalAccountSubscriptionRequestStartProcessing": "Start processing personal account subscription request", "Permissions:PersonalAccountSubscriptionRequestFinishProcessing": "Finish processing personal account subscription request", "Permissions:PersonalAccountSubscriptionRequestReject": "Reject personal account subscription request", "PermissionsGroup:RequestGroup": "Requests management", "Permissions:RequestIndex": "view all requests", "Permissions:RequestNoteIndex": "view all notes", "PermissionsGroup:DeviceGroup": "Devices management", "Permissions:DeviceIndex": "view all devices", "Permissions:DeviceCreate": "Add new device", "Permissions:DeviceDetails": " view device details", "PermissionsGroup:TrackAccountGroup": "Track accounts management", "Permissions:TrackAccountIndex": "view all track accounts", "Permissions:TrackAccountFeaturesDetails": "view track account features details", "Permissions:SetFeatureForTrackAccount": "Set feature for track account", "PermissionsGroup:FeatureGroup": "Features management", "Permissions:FeatureIndex": "view all features", "PermissionsGroup:AlertLogGroupName": "Alert log management", "Permissions:AlertLogIndex": "view all alert logs", "Permissions:TrackAccountDetails": "view Track account details", "Permissions:TrackAccountUserAssociations": "view all track account user associations", "Permissions:TrackAccountSubscriptions": "view all track account subscriptions", "Permissions:GetDeactivedDevicesByUser": "view all deactived device", "Permissions:AddVehiclesRequestGroupName": "Add vehicles request management", "Permissions:AddVehiclesRequestIndex": "view all add vehicles requests", "Permissions:AddVehiclesRequestDetails": "view add vehicles request details", "Permissions:AddVehiclesRequestInstallDevices": "Install Devices for add vehicles request", "Permissions:AddVehiclesRequestFinishProcessing": "Finish processing for add vehicles request", "PermissionsGroup:IncreaseUserCountRequestGroup": "Increase User Count Request Management", "Permissions:IncreaseUserCountRequestIndex": "View All Increase User Count Requests", "Permissions:IncreaseUserCountRequestDetails": "View Increase User Count Request Details", "Permissions:SubscriptionDurationDiscountIndex": "View All subscription duration discounts", "Permissions:SubscriptionDurationDiscountCreate": "Add new subscription duration discount", "Permissions:SubscriptionDurationDiscountDelete": "Delete subscription duration discount", "PermissionsGroup:RenewSubscriptionRequestGroup": "Renew Subscription Requests", "Permissions:RenewSubscriptionRequestAcceptRequest": "Accept Renew Subscription Request", "Permissions:RenewSubscriptionRequestIndex": "View Renew Subscription Requests", "Permissions:RenewSubscriptionRequestDetails": "Renew Subscription Request Details", "Permissions:RenewSubscriptionRequestFinishProcessing": "Finish Processing Renew Subscription Request", "Permissions:RenewSubscriptionRequestInstallDevices": "Install Devices for Renew Subscription", "Permissions:RenewSubscriptionRequestReject": "Reject Renew Subscription Request", "Permissions:RenewSubscriptionRequestRemovedVehicles": "View Removed Vehicles in Renew Subscription Request", "Permissions:RenewSubscriptionRequestNewVehicles": "View New Vehicles in Renew Subscription Request", "Permissions:RenewSubscriptionRequestRemovedUsers": "View Removed Users in Renew Subscription Request", "Permissions:RenewSubscriptionRequestRemainingVehicles": "View Remaining Vehicles from last Subscription after Renew Subscription Request", "Permissions:RenewSubscriptionRequestGetListNewVehiclesWithSearch": "View New Vehicles in Renew Subscription Request With Search", "Permissions:GetDeactivatedDevicesByUser": "view all deactivated device", "PermissionsGroup:RenewSMSPackageRequestGroupName": "Renew SMS Package Request", "Permissions:RenewSMSPackageRequestIndex": "View SMS Package Renewal Requests", "PermissionsGroup:PricingManagement": "Pricing Management", "Permissions:PricingIndex": "Access pricing list", "Permissions:PricingSet": "Set pricing", "Permissions:RequestPayWithCash": "Pay with Cash", "Permissions:AddVehiclesRequestManagementIndexAndSearchVehicles": "View Vehicles in Add Vehicles Request With Search", "Permissions:DiscountIndex": "View All discounts", "Permissions:DiscountCreate": "Add new discount", "Permissions:DiscountDelete": "Delete discount", "PermissionsGroup:PromoCode": "Promo Code Management", "Permissions:PromoCodeCreate": "Create Promo Codes", "Permissions:PromoCodeUpdateRange": "Update Promo Code Range", "Permissions:PromoCodeDeactive": "Deactivate Promo Codes", "Permissions:PromoCodeDetails": "View Promo Code Details", "Permissions:PromoCodeIndex": "View All Promo Codes", "PermissionsGroup:TrackAccountSubscriptionGroup": "Track Account Subscription", "Permissions:TrackAccountSubscriptionIndex": "View Track Account Subscriptions", "Permissions:TrackAccountSubscriptionDetails": "View Subscription Details", "Permissions:TrackAccountSubscriptionUpdateSettings": "Update Subscription Settings", "PermissionsGroup:SettingsGroup": "Settings Management", "Permissions:TrackAccountSubscriptionExpireReminderGet": "View Track Account Subscription Expire Re<PERSON><PERSON><PERSON><PERSON>", "Permissions:TrackAccountSubscriptionExpireReminderUpdate": "Update Track Account Subscription Expire Remind<PERSON> <PERSON><PERSON><PERSON>", "Permissions:SmsBundleExpireReminderGet": "View SMS Bundle Expire Remind<PERSON> Settings", "Permissions:SmsBundleExpireReminderUpdate": "Update SMS Bundle Expire Remind<PERSON><PERSON>s"}}