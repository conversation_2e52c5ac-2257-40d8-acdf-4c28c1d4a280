using FirebaseAdmin;
using Google.Apis.Auth.OAuth2;
using GoTrack.BackgroundTasks.Application;
using GoTrack.BackgroundTasks.Application.Contracts;
using GoTrack.EntityFrameworkCore;
using GoTrack.MultiTenancy;
using GoTrack.Options;
using Microsoft.Extensions.Options;
using Volo.Abp;
using Volo.Abp.AspNetCore.MultiTenancy;
using Volo.Abp.AspNetCore.Serilog;
using Volo.Abp.AuditLogging;
using Volo.Abp.Autofac;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Emailing;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.OpenIddict;
using Volo.Abp.PermissionManagement.Identity;
using Volo.Abp.PermissionManagement.OpenIddict;
using Volo.Abp.SettingManagement;
using Volo.Abp.TenantManagement;

namespace GoTrack.BackgroundTasks.Host;

[DependsOn(
    typeof(AbpAutofacModule),
    typeof(AbpAspNetCoreSerilogModule),
    typeof(AbpAspNetCoreMultiTenancyModule),
    typeof(GoTrackBackgroundTasksApplicationModule),
    typeof(GoTrackBackgroundTasksApplicationContractsModule),
    typeof(GoTrackEntityFrameworkCoreModule),
    typeof(AbpAuditLoggingDomainModule),
    typeof(AbpBackgroundJobsDomainModule),
    typeof(AbpFeatureManagementDomainModule),
    typeof(AbpIdentityDomainModule),
    typeof(AbpOpenIddictDomainModule),
    typeof(AbpPermissionManagementDomainOpenIddictModule),
    typeof(AbpPermissionManagementDomainIdentityModule),
    typeof(AbpSettingManagementDomainModule),
    typeof(AbpTenantManagementDomainModule),
    typeof(AbpEmailingModule)
)]
public class GoTrackBackgroundTasksHostModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        context.Services.AddOptions<FirebaseOptions>()
            .Bind(context.Services.GetConfiguration().GetSection("Firebase"))
            .ValidateDataAnnotations()
            .ValidateOnStart();
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpBackgroundJobOptions>(options => { options.IsJobExecutionEnabled = true; });

        var serviceProvider = context.Services.BuildServiceProvider();
        var fcmOptions = serviceProvider.GetRequiredService<IOptions<FirebaseOptions>>().Value;

        if (FirebaseApp.DefaultInstance is null)
        {
            var credential = GoogleCredential.FromJsonParameters(new JsonCredentialParameters
            {
                Type = fcmOptions.Type,
                ProjectId = fcmOptions.ProjectId,
                PrivateKeyId = fcmOptions.PrivateKeyId,
                PrivateKey = fcmOptions.PrivateKey,
                ClientEmail = fcmOptions.ClientEmail,
                ClientId = fcmOptions.ClientId,
                TokenUri = fcmOptions.TokenUri,
                UniverseDomain = fcmOptions.UniverseDomain
            });

            FirebaseApp.Create(new AppOptions
            {
                Credential = credential,
                ProjectId = fcmOptions.ProjectId,
            });
        }

        var configuration = context.Services.GetConfiguration();
        var hostingEnvironment = context.Services.GetHostingEnvironment();

    }

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        var env = context.GetEnvironment();

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseAbpRequestLocalization();

        app.UseCorrelationId();
        app.UseStaticFiles();
        app.UseRouting();

        if (MultiTenancyConsts.IsEnabled)
        {
            app.UseMultiTenancy();
        }

        app.UseUnitOfWork();

        app.UseAuditing();
        app.UseAbpSerilogEnrichers();
        app.UseConfiguredEndpoints();
    }
}
