using GoTrack.EntityFrameworkCore;
using GoTrack.SeedWork;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Collections.Generic;
using System.Text.Json;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.Devices.EntityConfiguration;

public class DeviceEntityConfiguration : IEntityTypeConfiguration<Device>
{
    public void Configure(EntityTypeBuilder<Device> builder)
    {
        builder.ConfigureByConvention();

        builder.Property(sp => sp.Brand)
            .IsRequired()
            .HasConversion(
                s => JsonSerializer.Serialize(s.LValue, new JsonSerializerOptions()),
                s => new LocalizedString(JsonSerializer.Deserialize<Dictionary<string, string>>(s, new JsonSerializerOptions()))
            );

        builder.Property(sp => sp.Model)
            .IsRequired()
            .HasConversion(
                s => JsonSerializer.Serialize(s.LValue, new JsonSerializerOptions()),
                s => new LocalizedString(JsonSerializer.Deserialize<Dictionary<string, string>>(s, new JsonSerializerOptions()))
            );

        var navigation = builder.Metadata.FindNavigation(nameof(Device.StatusLogs));
        navigation?.SetPropertyAccessMode(PropertyAccessMode.Field);

        builder.HasMany(x => x.StatusLogs);

        builder.ToGoTrackTable();
    }
}
