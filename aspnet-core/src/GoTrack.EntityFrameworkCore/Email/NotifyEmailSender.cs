using Notify.Provider.Email;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.MailKit;

namespace GoTrack.Email;

public class NotifyEmailSender : INotifyEmailSender, ITransientDependency
{
    private readonly IMailKitSmtpEmailSender _emailSender;

    public NotifyEmailSender(IMailKitSmtpEmailSender emailSender)
    {
        _emailSender = emailSender;
    }

    public Task SendAsync(EmailMessage emailMessage)
    {
        return _emailSender.SendAsync(
            to: emailMessage.Email,
            subject: emailMessage.Subject,
            body: emailMessage.Body,
            isBodyHtml: false
        );
    }
}
