using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using FirebaseAdmin.Messaging;
using GoTrack.BackgroundJobs.JobArguments;
using GoTrack.FCMDevices;
using Microsoft.Extensions.Logging;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.DependencyInjection;

namespace GoTrack.Notifications;

public class FirebaseNotificationSender : ITransientDependency, IFirebaseNotificationSender
{
    private readonly IBackgroundJobManager _backgroundJobManager;
    private readonly ILogger<FirebaseNotificationSender> _logger;
    private readonly FcmDeviceManager _fcmDeviceManager;

    public FirebaseNotificationSender(IBackgroundJobManager backgroundJobManager, ILogger<FirebaseNotificationSender> logger, FcmDeviceManager fcmDeviceManager)
    {
        _backgroundJobManager = backgroundJobManager;
        _logger = logger;
        _fcmDeviceManager = fcmDeviceManager;
    }

    public async Task<IEnumerable<string>> SendPushNotificationAsync(string title, string body, List<string> fcmTokens, Dictionary<string, string>? data = null)
    {
        if (fcmTokens.Count is 0) return [];

        const int size = 500;
        var batches = fcmTokens.Chunk(size);

        var failedTokens = new List<string>();
        var invalidTokens = new List<string>();

        await Parallel.ForEachAsync(batches, async (batch, cancellationToken) =>
        {
            var message = new MulticastMessage
            {
                Tokens = [.. batch],
                Notification = new Notification { Title = title, Body = body },
                Data = data ?? []
            };

            try
            {
                var response = await FirebaseMessaging.DefaultInstance.SendEachForMulticastAsync(message, cancellationToken);

                for (int i = 0; i < response.Responses.Count; i++)
                {
                    if (response.Responses[i].IsSuccess)
                    {
                        continue;
                    }
                    
                    var failedToken = batch.ElementAt(i);
                    var error = response.Responses[i].Exception;

                    if (error is FirebaseMessagingException fcmException)
                    {
                        switch (fcmException.MessagingErrorCode)
                        {
                            case MessagingErrorCode.QuotaExceeded:
                            case MessagingErrorCode.Unavailable:
                                failedTokens.Add(failedToken);
                                break;

                            case MessagingErrorCode.SenderIdMismatch:
                            case MessagingErrorCode.Unregistered:
                            case MessagingErrorCode.InvalidArgument:
                            case MessagingErrorCode.Internal:
                                invalidTokens.Add(failedToken);
                                break;
                            case MessagingErrorCode.ThirdPartyAuthError:
                                break;
                            case null:
                                break;
                            default:
                                throw new ArgumentOutOfRangeException();
                        }
                    }
                }

                if (invalidTokens.Count is not 0)
                {
                    _ = invalidTokens.Select(async invalidToken => await _fcmDeviceManager.DeactivateDeviceAsync(invalidToken));
                }
                
                if (failedTokens.Count is not 0)
                {
                    await _backgroundJobManager.EnqueueAsync(new ResendPushNotificationArgs
                    {
                        Title = title,
                        Body = body,
                        FcmTokens = failedTokens,
                        Data = data
                    });
                }
            }
            catch (HttpRequestException)
            {
                _logger.LogError("Network error,sending to background job for retry");

                await _backgroundJobManager.EnqueueAsync(new ResendPushNotificationArgs
                {
                    Title = title,
                    Body = body,
                    FcmTokens = [.. batch],
                    Data = data
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Unexpected error: {ex.Message}");
            }
        });

        return invalidTokens.Concat(failedTokens);
    }
}