using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Linq;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.AlertDefinitions.EntityConfiguration;

public class AlertDefinitionEntityConfiguration : IEntityTypeConfiguration<AlertDefinition>
{
    private static readonly char separator = ',';

    public void Configure(EntityTypeBuilder<AlertDefinition> builder)
    {
        builder.ConfigureByConvention();

        builder.UseTptMappingStrategy();

        builder.Property(x => x.NotificationMethods)
            .HasConversion(
                v => string.Join(separator, v.Select(s => s.ToString())), // Convert enum list to a comma-separated string

                v => v.Split(separator, StringSplitOptions.RemoveEmptyEntries)
                      .Select(s => (AlertDefinitionNotificationMethod)Enum.Parse(typeof(AlertDefinitionNotificationMethod), s))
                      .ToList()
            );

        builder.ToGoTrackTable();
    }
}
