using GoTrack.Payments.PricingItems;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.EntityFrameworkCore.GoTrackEntityConfigurations.Payments;

public class PricingItemEntityConfiguration : IEntityTypeConfiguration<PricingItem>
{
    public void Configure(EntityTypeBuilder<PricingItem> builder)
    {
        builder.ConfigureByConvention();
        builder.ToGoTrackTable();
        builder.OwnsMany(x => x.PriceHistory);
        builder.HasIndex(x => x.Key).IsUnique();
    }
}