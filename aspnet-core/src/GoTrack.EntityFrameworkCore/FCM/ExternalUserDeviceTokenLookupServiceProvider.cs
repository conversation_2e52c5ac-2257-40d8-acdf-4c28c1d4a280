using GoTrack.FCMDevices;
using GoTrack.Identity;
using GoTrack.TrackAccounts;
using Notify.Provider.FCM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;

namespace GoTrack.FCM;

public class ExternalUserDeviceTokenLookupServiceProvider : IExternalUserDeviceTokenLookupServiceProvider, ITransientDependency
{
    private readonly IDataFilter _dataFilter;
    private readonly FcmDeviceManager _fcmDeviceManager;

    public ExternalUserDeviceTokenLookupServiceProvider(
        IDataFilter dataFilter,
        FcmDeviceManager fcmDeviceManager)
    {
        _dataFilter = dataFilter;
        _fcmDeviceManager = fcmDeviceManager;
    }

    public async Task<List<string>> GetTokensByUserId(Guid userId, CancellationToken cancellationToken = default)
    {
        using var _ = _dataFilter.Disable<IHaveTrackAccount>();
        using var __ = _dataFilter.Disable<IHostTenantUserFilter>();
        using var ___ = _dataFilter.Disable<ICustomerUserFilter>();

        var fcmDevices = await _fcmDeviceManager.GetUserDevicesAsync(userId);
        
        if (fcmDevices.IsNullOrEmpty())
            return [];

        return fcmDevices.Select(x => x.FcmToken).ToList();
    }
}
