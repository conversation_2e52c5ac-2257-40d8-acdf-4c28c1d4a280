using GoTrack.Mobile.Coordinates.DTOs;
using GoTrack.Mobile.StopPoints.DTOs;
using System.Collections.Generic;

namespace GoTrack.Mobile.Routes.DTOs;

public class CreateRouteDto
{
    public string Name { get; set; } = string.Empty;
    public List<CreateCoordinateDto> Line { get; set; } = new();
    public string HexColor { get; set; } = string.Empty;
    public List<CreateStopPointDto> StopPoints { get; set; } = new();
}
