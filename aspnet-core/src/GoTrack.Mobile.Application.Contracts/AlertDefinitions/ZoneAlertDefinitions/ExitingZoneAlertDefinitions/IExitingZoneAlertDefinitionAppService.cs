using GoTrack.Mobile.AlertDefinitions.ZoneAlertDefinitions.ExitingZoneAlertDefinitions.DTOs;
using GoTrack.Mobile.GeoZones;
using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace GoTrack.Mobile.AlertDefinitions.ZoneAlertDefinitions.ExitingZoneAlertDefinitions;

public interface IExitingZoneAlertDefinitionAppService : IApplicationService
{
    Task CreateAsync(CreateExitingZoneAlertDefinitionDto input);
    Task<ExitingZoneAlertDefinitionDto> GetAsync(Guid id);
    Task<PagedResultDto<GeoZoneDetailsDto>> GetGeoZonesAsync(Guid id, PagedResultRequestDto input);
    Task<PagedResultDto<ExitingZoneAlertDefinitionDto>> GetListAsync(PagedResultRequestDto input);
}
