using System;
using System.Collections.Generic;
using GoTrack.SubscriptionPlans;

namespace GoTrack.Mobile.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests;

public class PersonalAccountSubscriptionRequestDetailsDto : RequestDto
{
    public Guid OwnerId { get; set; }
    public string AccountName { get; set; } = string.Empty;
    public string TrackerInstallationLocation { get; set; } = string.Empty;
    public List<SubscriptionVehicleInfoDto> TrackVehicles { get; set; } = [];
    public List<RequestNoteDto> RequestNotes { get; set; } = [];
    public SubscriptionPlan SubscriptionPlan { get; set; }
    public string Stage { get; set; }
    public decimal Price { get; set; }
    public int UserCount { get; set; }
    public Guid? SmsBundleId { get; set; }
    public int SubscriptionDurationInMonths { get; set; }
    public string? PaymentUrl { get; set; }
}