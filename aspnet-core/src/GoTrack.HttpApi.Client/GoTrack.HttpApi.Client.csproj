<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>GoTrack</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\GoTrack.Application.Contracts\GoTrack.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Account.HttpApi.Client" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.Identity.HttpApi.Client" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.PermissionManagement.HttpApi.Client" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.TenantManagement.HttpApi.Client" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.FeatureManagement.HttpApi.Client" Version="8.3.0" />
    <PackageReference Include="Volo.Abp.SettingManagement.HttpApi.Client" Version="8.3.0" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="**\*generate-proxy.json" />
    <Content Remove="**\*generate-proxy.json" />
  </ItemGroup>

</Project>
