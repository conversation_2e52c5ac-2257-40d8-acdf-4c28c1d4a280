using System.Text;
using Warp10Abstraction;
using Warp10Abstraction.Models;

namespace Warp10.Scripts
{
    public static class ReportScripts
    {
        public static async Task<ViolationResult> CheckDisconnectionPeriods(this WarpService warpService,
            string deviceImei,
            DateTime fromDate,
            DateTime toDate,
            int minDifference)
        {
            string script = "'" + WarpHelper.ConvertDate(fromDate) + @"' 'startDate' STORE
                                '" + WarpHelper.ConvertDate(toDate) + @"' 'endDate' STORE
                                '" + deviceImei + @"' 'imei' STORE 
                                " + minDifference + @" 'minDifference' STORE 
                                    // <%
                                    // Get the speed data in the selected period
                                    [ $readToken 'server_time' { 'imei' $imei } $startDate $endDate ] FETCH 
                                    'all_data' STORE
                                    <%
                                        <% $all_data SIZE 0 == %>
                                        <%
                                            { 'Result' true 'Data' { 'ViolatedIntervals' [] 'FirstTimestamp' 0 'LastTimestamp' 0 } }  
                                        %>
                                        <%
                                            $all_data TICKS DUP 0 GET 'first_timestamp' STORE DUP SIZE 1 - GET 'last_timestamp' STORE
            
                                                0 'n' STORE
                                                0 'first_point' STORE

                                                // Split GTS by time difference and get only violated ranges
                                                [ $all_data
                                                <%
                                                    // $ele 3 GET 0 GET // time
                                                    // $ele 4 GET 0 GET // loc1
                                                    // $ele 5 GET 0 GET // loc2
                                                    // $ele 6 GET 0 GET // alt
                                                    // $ele 7 GET 0 GET // val
                                                    'ele_window' STORE
                                                    $ele_window 7 GET 'value_list' STORE
                                                    $ele_window 3 GET 'timestamp_list' STORE

                                                    $value_list 0 GET 'server_time' STORE
                                                    $timestamp_list 0 GET 'device_time' STORE

                                                    $server_time $device_time - $minDifference > 'current_value' STORE

                                                    <% $value_list SIZE 1 == %>
                                                    <%
                                                        { 
                                                            'split-' $n TOSTRING + 
                                                            [ 
                                                                $device_time // time
                                                                $current_value // val
                                                            ]
                                                        }
                                                    %>
                                                    <%
                                                        $value_list 1 GET 'np_server_time' STORE
                                                        $timestamp_list 1 GET 'np_device_time' STORE

                                                        $np_server_time $np_device_time - $minDifference > 'next_value' STORE

                                                        <% $first_point 0 == %>
                                                        <%
                                                            1 'first_point' STORE
                                                            { 
                                                                'split-' $n TOSTRING + 
                                                                [ 
                                                                    $device_time // time
                                                                    $current_value // val
                                                                ]
                                                            }
                                                            <% $next_value $current_value !=  %>
                                                            <%
                                                                0 'first_point' STORE
                                                                $n 1 + 'n' STORE
                                                            %> IFT
                                                        %>
                                                        <%
                                                            <% $next_value $current_value !=  %>
                                                            <%
                                                                0 'first_point' STORE
                                                                { 
                                                                    'split-' $n TOSTRING + 
                                                                    [ 
                                                                        $device_time // time
                                                                        $current_value // val
                                                                    ]
                                                                }
                                                                $n 1 + 'n' STORE
                                                            %>
                                                            <%
                                                            { 
                                                                'split-' $n TOSTRING + 
                                                                [ 
                                                                    $device_time // time
                                                                    NULL // val
                                                                ]
                                                            }
                                                            %>
                                                            IFTE
                                                        %>
                                                        IFTE
                                                    %> IFTE
                                                %> MACROMAPPER
                                                0 1 0 ] MAP 
                                                <% $n 0 > %>
                                                <%  
                                                    0 GET
                                                %> IFT
                                                [ SWAP [] true filter.all.eq ] FILTER 'result' STORE

                                                { 'Result' true 'Data' { 'ViolatedIntervals' $result 'FirstTimestamp' $first_timestamp 'LastTimestamp' $last_timestamp } }  
            
                                        %>
                                        IFTE
                                    %>
                                    <% { 'Result' false 'Exception' ERROR } %>
                                    <% %>
                                    TRY";
            try
            {
                return WarpHelper.CheckWarpResponse(
                    await warpService.ExecScript<WarpResponse<ViolationResult>>(script));
            }
            catch
            {
                return null;
            }
        }

        public static async Task<JourneyDetails[]> GetJourneysDetails(this WarpService warpService, string[] imeis,
            DateTime fromDate, DateTime toDate, int distance = 28000)
        {
            var stringImeis = WarpHelper.GetMultiValuesLabelFilter(imeis);
            string script = WarpHelper.ConvertDate(fromDate) + @" 'startDate' STORE
                            " + WarpHelper.ConvertDate(toDate) + @" 'endDate' STORE
                            '" + stringImeis + @"' 'imei' STORE
                            " + distance + @" 'distance_split' STORE

                            [ $readToken $startDate $endDate $imei $distance_split ] @report/JOURNEYSTATS
                            'all_distance' STORE
                            { 'Result' true 'Data' $all_distance }  
                            ";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<JourneyDetails[]>>(script));
        }

        public static async Task<double> GetTravelledDistance(this WarpService warpService, string imei,
            List<DateTime> fromDate, List<DateTime> toDate, int distance = 28000, int ignition_value = -1)
        {
            string script = "'" + imei + @"' 'imei' STORE
                            " + distance + @" 'distance_split' STORE
                            " + ignition_value + @" 'ignition_value' STORE


                            []
";
            foreach (DateTime dateTime in fromDate)
            {
                script += " " + WarpHelper.ConvertDate(dateTime) + " +! ";
            }
            script += @"
                            'startList' STORE

                            []
";
            foreach (DateTime dateTime in toDate)
            {
                script += " " + WarpHelper.ConvertDate(dateTime) + " +! ";
            }
            script += @"
                            'endList' STORE



                            [ $readToken $startList $endList $imei $distance_split $ignition_value false NULL ] @report/MOVEDDIStANCE
                            'all_distance' STORE
                            { 'Result' true 'Data' $all_distance }  
                            ";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<double>>(script), script);
        }

        public static async Task<ImeiDistanceSpeedStat> GetTravelledDistanceSpeedStats(this WarpService warpService, string imei,
            DateTime fromDate, DateTime toDate, double ignoreSpeedUnder, int distance = 28000, bool withIgnition = false)
        {
            string script = WarpHelper.ConvertDate(fromDate) + @" 'startDate' STORE
                            " + WarpHelper.ConvertDate(toDate) + @" 'endDate' STORE
                            '" + imei + @"' 'imei' STORE
                            " + distance + @" 'distance_split' STORE
                            " + ignoreSpeedUnder + @" 'ignore_speed_under' STORE
                            " + (withIgnition?"true":"false") + @" 'with_ignition' STORE

                            [ $readToken $startDate $endDate $imei $distance_split $with_ignition false NULL $ignore_speed_under ] @report/DISTANCESPEEDSTAT
                            'all_distance' STORE
                            { 'Result' true 'Data' $all_distance }  
                            ";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<ImeiDistanceSpeedStat>>(script));
        }


        /// <summary>
        /// calc Travelled Distance on route over a period of time
        /// </summary>
        /// <param name="HHCodes">List of HHCodes strings "cb4e3e142" +! ..." </param>
        /// <param name="fromDate">must be in unix timestamp format </param>
        /// <param name="toDate">must be in unix timestamp format </param>
        public static async Task<double> GetTravelledDistanceInRoute(this WarpService warpService,
            string imei, List<string> HHCodes,
            List<DateTime> fromDate, List<DateTime> toDate, int distance = 28000, int ignition_value = -1)
        {
            string script = "'" + imei + @"' 'imei' STORE
                            " + distance + @" 'distance_split' STORE
                            " + ignition_value + @" 'ignition_value' STORE
                            [ ] 'zoneList' STORE
                            ";
            foreach (string HHCode in HHCodes)
            {
                script += @"
                            $zoneList
                            []
                            " + HHCode + @"
                            ->GEOSHAPE
                            +! 'zoneList' STORE
                        ";
            }

            script += @"
                            $zoneList GEO.UNION 'zone' STORE

                            []
";
            foreach (DateTime dateTime in fromDate)
            {
                script += " " + WarpHelper.ConvertDate(dateTime) + " +! ";
            }
            script += @"
                            'startList' STORE

                            []
";
            foreach (DateTime dateTime in toDate)
            {
                script += " " + WarpHelper.ConvertDate(dateTime) + " +! ";
            }
            script += @"
                            'endList' STORE


                            [ $readToken $startList $endList $imei 28000 $ignition_value true $zone ] @report/MOVEDDIStANCE
                            'all_distance' STORE
                            { 'Result' true 'Data' $all_distance }  
                            ";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<double>>(script));
        }

        //Polygon 36.32097244262696 33.561353910359486,36.32264614105225 33.559780407081526,36.32419109344483 33.553700693255784,36.321637630462654 33.551804047905755,36.31816148757935 33.55023037065896,36.319620609283454 33.5479592177360860
        //imei 359632104310864
        //StartDate 1650488400000
        //EndDate 165057480000
        public static async Task<ImeiDistanceSpeedStat> GetTravelledDistanceSpeedStatsInRoute(this WarpService warpService,
            string imei, List<string> polygons,
            DateTime fromDate, DateTime toDate, double ignoreSpeedUnder, int distance = 28000, bool withIgnition = false)
        {
            string script = WarpHelper.ConvertDate(fromDate) + @" 'startDate' STORE
                            " + WarpHelper.ConvertDate(toDate) + @" 'endDate' STORE
                            '" + imei + @"' 'imei' STORE
                            " + distance + @" 'distance_split' STORE
                            " + ignoreSpeedUnder + @" 'ignore_speed_under' STORE
                            " + (withIgnition ? "true" : "false") + @" 'with_ignition' STORE
                            [ ] 'zoneList' STORE
                            ";
            foreach (string polygon in polygons)
            {
                script += @"
                            $zoneList 
                            []
                            " + polygon + @"
                            ->GEOSHAPE
                            +! 'zoneList' STORE
                        ";
            }
            script += @"
                            $zoneList GEO.UNION 'zone' STORE

                            [ $readToken $startDate $endDate $imei 28000 $with_ignition true $zone $ignore_speed_under ] @report/DISTANCESPEEDSTAT
                            'all_distance' STORE
                            { 'Result' true 'Data' $all_distance }  
                            ";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<ImeiDistanceSpeedStat>>(script));
        }


        public static async Task<IntervalResult> CalculateStoppingInterval(this WarpService warpService,
            string deviceImei, int stopped_min_time_seconds,
            int stopped_max_speed_kmh, int stopped_max_mean_speed_kmh, int stopped_max_radius_meters,
            string fromDate, string toDate)
        {
            string script = "'" + fromDate + @"' 'startDate' STORE
                            '" + toDate + @"' 'endDate' STORE
                            '" + deviceImei + @"' 'imei' STORE "
                            + stopped_min_time_seconds + @" 'stopped_min_time_seconds' STORE "
                            + stopped_max_speed_kmh + @" 'stopped_max_speed_km/h' STORE "
                            + stopped_max_mean_speed_kmh + @" 'stopped_max_mean_speed_km/h' STORE "
                            + stopped_max_radius_meters + @" 'stopped_max_radius_meters' STORE "
                            + @"
                           <% 
                                [ $readToken 'speed' { 'imei' $imei } $startDate $endDate ] FETCH
                                'all_data' STORE
                                <% $all_data SIZE 0 == %>
                                <%
                                    { 'Result' true 'Data' { 'Interval' 0 } }  
                                %>
                                <%
                                    // Check for Gaps in records (more than 2 minutes without any reading)
                                    <% 3.6 / %> 'km/h' STORE

                                    $all_data
                                    {
                                    'stopped.min.time'  $stopped_min_time_seconds s
                                    'stopped.max.speed' $stopped_max_speed_km/h @km/h
                                    'stopped.max.radius' $stopped_max_radius_meters
                                    'stopped.max.mean.speed' $stopped_max_mean_speed_km/h @km/h
                                    'stopped.split' true
                                    'label.split.number' 'split'
                                    'label.split.type' 'reason'
                                    }
                                    MOTIONSPLIT
                                    0 GET

                                    [
                                    SWAP
                                    []
                                    { 'reason' '=stopped' }
                                    filter.bylabels
                                    ] FILTER
                                    0 'res' STORE
                                    <%
                                        TICKS 'ticks' STORE 
                                        $ticks -1 GET 
                                        $ticks 0 GET
                                        -
                                        $res
                                        +
                                        'res' STORE
                                    %>
                                    FOREACH

                                    { 'Result' true 'Data' { 'Interval' $res } }  
                                %> 
                                IFTE
                            %>
                            <% { 'Result' false 'Exception' ERROR } %>
                            <% %>
                            TRY";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<IntervalResult>>(script));
        }


        /// <summary>
        /// Get the devices that did not send any data after a specified time
        /// </summary>
        /// <param name="warpService"></param>
        /// <param name="fromDate">The date in milli-seconds after which we want the disconnected devices </param>
        /// <returns></returns>
        public static async Task<WarpGTS[]> DisconnectedDevicesAfterDate(this WarpService warpService,
            DateTime fromDate)
        {
            string script = "'" + WarpHelper.ConvertDate(fromDate) + @"' TOLONG 'fromDate' STORE
                        <%
                            {
                                'quiet.after' $fromDate
                                'selectors' [ 'server_time{imei~.*}' ]
                                'token' $readToken
                            } FIND 'result' STORE
                            { 'Result' true 'Data' $result }  
                        %>
                        <% { 'Result' false 'Exception' ERROR } %>
                        <% %>
                        TRY";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<WarpGTS[]>>(script));
        }

        public static async Task<WarpGTS[]> GetQuietDevicesSinceDateAndActiveInLast10Days(this WarpService warpService,
            DateTime quietAfter)
        {
            var script = new StringBuilder();
            
            script.AppendFormat("'{0}' TOLONG 'activeAfter' STORE ",WarpHelper.ConvertDate(quietAfter.AddDays(-10).Date));
            script.AppendFormat("'{0}' TOLONG 'quietAfter' STORE ",WarpHelper.ConvertDate(quietAfter));
            script.Append(
                @"<%
                    {
                        'active.after' $activeAfter
                        'quiet.after' $quietAfter
                        'selectors' [ 'server_time{imei~.*}' ]
                        'token' $readToken
                    } FIND 'result' STORE
                { 'Result' true 'Data' $result }  
                %>
                <% { 'Result' false 'Exception' ERROR } %>
                <% %>
                TRY"
            );
           
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<WarpGTS[]>>(script.ToString()));
        }
        
        /// <summary>
        /// Get the devices that has been sending data after a specified time
        /// </summary>
        /// <param name="warpService"></param>
        /// <param name="fromDate">The date in milli-seconds after which we want the active devices </param>
        /// <returns></returns>
        public static async Task<WarpGTS[]> ActiveDevicesAfterDate(this WarpService warpService,
            DateTime fromDate)
        {
            string script = "'" + WarpHelper.ConvertDate(fromDate) + @"' TOLONG 'fromDate' STORE
                        <%
                            {
                                'active.after' $fromDate
                                'selectors' [ 'server_time{imei~.*}' ]
                                'token' $readToken
                            } FIND 'result' STORE
                            { 'Result' true 'Data' $result }  
                        %>
                        <% { 'Result' false 'Exception' ERROR } %>
                        <% %>
                        TRY";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<WarpGTS[]>>(script));
        }

        /// <summary>
        /// Get the devices exist in warp10
        /// </summary>
        /// <param name="warpService"></param>
        /// <returns></returns>
        public static async Task<WarpGTS[]> ActiveDevices(this WarpService warpService)
        {
            string script = @"
                        <%
                            {
                                'selectors' [ 'server_time{imei~.*}' ]
                                'token' $readToken
                            } FIND 'result' STORE
                            { 'Result' true 'Data' $result }  
                        %>
                        <% { 'Result' false 'Exception' ERROR } %>
                        <% %>
                        TRY";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<WarpGTS[]>>(script));
        }

        /// <summary>
        /// Get the devices last speed packet la in warp10
        /// </summary>
        /// <param name="warpService"></param>
        /// <returns></returns>
        public static async Task<WarpGTS[]> LastSpeedPacket(this WarpService warpService)
        {
            string script = @"
                        <%
                            {
                                'selectors' [ 'speed{imei~.*}' ]
                                'token' $readToken
                            } FIND 'result' STORE
                            { 'Result' true 'Data' $result }  
                        %>
                        <% { 'Result' false 'Exception' ERROR } %>
                        <% %>
                        TRY";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<WarpGTS[]>>(script));
        }

        public static async Task<WarpGTS[]> SpeedActivity(this WarpService warpService, string[] imeis,
            DateTime fromDate, DateTime toDate)
        {
            string recordLabel = WarpHelper.GetMultiValuesLabelFilter(imeis);
            string script = "'" + WarpHelper.ConvertDate(fromDate) + @"' 'startDate' STORE
                            '" + WarpHelper.ConvertDate(toDate) + @"' 'endDate' STORE
                         '" + recordLabel + @"'
                         'imei' STORE
                         <%
                            [ $readToken 'speed' { 'imei' $imei } $startDate $endDate ] FETCH 
                            [ SWAP 0.0 mapper.gt 0 0 0 ] MAP 'res' STORE
                            { 'Result' true 'Data' $res }  
                        %>
                        <% { 'Result' false 'Exception' ERROR } %>
                        <% %>
                        TRY
                    ";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<WarpGTS[]>>(script));
        }

        public static async Task<ImeiPair[]> FraudStickyDevices(this WarpService warpService, string[] imeis,
            DateTime fromDate, DateTime toDate, float pointsPercentage, float matchPercentage, int interval,
            int distanceBetween, int overAllDistance, int minutesClose)
        {
            string recordLabel = WarpHelper.GetMultiValuesLabelFilter(imeis);
            string script = "'" + WarpHelper.ConvertDate(fromDate) + @"' 'startDate' STORE
                            '" + WarpHelper.ConvertDate(toDate) + @"' 'endDate' STORE
                        " + interval + @" m 'interval' STORE
                        " + matchPercentage + @" 'match_percentage' STORE
                        " + pointsPercentage + @" 'points_percentage' STORE
                        " + distanceBetween + @" 'distance_between' STORE
                        " + overAllDistance + @" 'over_all_distance' STORE
                        " + minutesClose + @" 'minutes_close' STORE
                        0 'page_num' STORE
                        1000 'page_size' STORE
                         '" + recordLabel + @"'
                         'imei' STORE
                         
                         <%
                            $endDate TOLONG $startDate TOLONG - 1000 / 60 / $points_percentage * 2 * ROUND
                            'min_points' STORE
                            [ $readToken 'speed' { 'imei' $imei } $startDate $endDate ] FETCH
                            [ SWAP 0.0 mapper.gt 0 0 0 ] MAP 
                            [ SWAP [] $min_points 10000000000 filter.bysize ] FILTER
                            'all_data' STORE
                            $all_data [ SWAP mapper.hdist 0 10000000000 1 ] MAP 'hdis' STORE
                            $hdis SIZE 'siz' STORE
                            0 $siz 1 -
                            <%
                                -1 * $siz 1 - + 'i' STORE
                                $hdis $i GET VALUES 0 GET 1 <
                                <%
                                    $all_data $i REMOVE DROP 'all_data' STORE
                                    $hdis $i REMOVE DROP 'hdis' STORE
                                %> IFT
                            %> FOR
                            $all_data SIZE 'list_size' STORE
                            [] 'res' STORE
                            $page_num $page_size * $page_num $page_size * $page_size + 1 -
                            <%
                                'i' STORE
                                
                                $i 1 + $list_size 1 -
                                <%
                                    'j' STORE
                                    $all_data $i GET 'gts1' STORE
                                    $all_data $j GET 'gts2' STORE

                                    $hdis $i GET VALUES 0 GET $hdis $j GET VALUES 0 GET - ABS $over_all_distance >
                                    <% CONTINUE %> IFT
                                    
                                    0 'matches_count' STORE
                                    0 'all_count' STORE
                                    
                                    $gts1 TICKLIST 'first_tick_list' STORE
                                    $gts2 TICKLIST 'second_tick_list' STORE
                                    $gts1 LOCATIONS 'first_long_list' STORE 'first_lat_list' STORE
                                    $gts2 LOCATIONS 'second_long_list' STORE 'second_lat_list' STORE
                                    0 'index_1' STORE
                                    0 'index_2' STORE
                                    true 'can_cont' STORE

                                    $startDate TOLONG 'check_tick' STORE
                                    <% $check_tick $endDate TOLONG < $can_cont AND %>
                                    <%
                                        <% $first_tick_list $index_1 GET $check_tick <= %>
                                        <%
                                            $index_1 1 + 'index_1' STORE
                                            $index_1 $first_tick_list SIZE >=
                                            <% false 'can_cont' STORE BREAK %> IFT
                                        %> WHILE
                                        $can_cont NOT
                                        <% BREAK %> IFT

                                        <% $second_tick_list $index_2 GET $check_tick <= %>
                                        <%
                                            $index_2 1 + 'index_2' STORE
                                            $index_2 $second_tick_list SIZE >=
                                            <% false 'can_cont' STORE BREAK %> IFT
                                        %> WHILE
                                        $can_cont NOT
                                        <% BREAK %> IFT
                                        
                                        <% 
                                            $first_tick_list $index_1 GET $check_tick - $minutes_close m <= 
                                        $second_tick_list $index_2 GET $check_tick - $minutes_close m <= AND
                                        %>
                                        <%
                                            $first_long_list $index_1 GET 'first_long' STORE
                                            $first_lat_list $index_1 GET 'first_lat' STORE
                                            $second_long_list $index_2 GET 'second_long' STORE
                                            $second_lat_list $index_2 GET 'second_lat' STORE

                                            $all_count 1 + 'all_count' STORE

                                            $first_lat
                                            $first_long
                                            $second_lat
                                            $second_long
                                            HAVERSINE 
                                            $distance_between <
                                            
                                            <%
                                                $matches_count 1 + 'matches_count' STORE
                                            %> IFT
                                            
                                            $index_1 1 + 'index_1' STORE
                                            $index_1 $first_tick_list SIZE >=
                                            <% BREAK %> IFT
                                            
                                            $index_2 1 + 'index_2' STORE
                                            $index_2 $second_tick_list SIZE >=
                                            <% BREAK %> IFT
                                        %> IFT

                                        $check_tick $interval + 'check_tick' STORE
                                    %> WHILE
                                    <% $all_count 0 > %>
                                    <%
                                        <% $matches_count $all_count / $match_percentage >
                                        $all_count 1.0 * $endDate TOLONG $startDate TOLONG - $interval / / $points_percentage >
                                        AND  %>
                                        <%
                                            $res
                                            {
                                                'imei1' $gts1 LABELS 'imei' GET 
                                                'imei2' $gts2 LABELS 'imei' GET
                                                'all' $all_count
                                                'match' $matches_count
                                            } +! 'res' STORE
                                        %> IFT
                                    %> IFT
                                %> FOR
                            %> FOR
                            { 'Result' true 'Data' $res }  
                        %>
                        <% { 'Result' false 'Exception' ERROR } %>
                        <% %>
                        TRY";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<ImeiPair[]>>(script, true));
        }

        public static async Task<string[]> OutliersDevices(this WarpService warpService, DateTime fromDate,
            DateTime toDate, int distanceSplit =10000, int secondsSplit = 60)
        {
            string script = "'" + WarpHelper.ConvertDate(fromDate) + @"' 'startDate' STORE
                            '" + WarpHelper.ConvertDate(toDate) + @"' 'endDate' STORE
                        " + distanceSplit + @" 'distance_split' STORE
                        " + secondsSplit + @" 'seconds_split' STORE

                        [ ] ->SET 'sett' STORE
                        <%
                            [ $readToken 'speed' { } $startDate $endDate ] FETCH
                            {
                                'distance.split' $distance_split
                                'label.split.number' 'distancesplit'
                            }
                            MOTIONSPLIT
                            <%
                                DUP
                                <% SIZE 1 == %>
                                <%
                                    DROP
                                %>
                                <%
                                    0 'pre' STORE
                                    <%
                                        DUP
                                        TICKS DUP 0 GET 'first' STORE DUP SIZE 1 - GET 'last' STORE
                                        LABELS 'imei' GET 'immmm' STORE
                                        <% $pre 0 != $first $pre - $seconds_split TOLONG 1000 * < AND %>
                                        <%
                                            ( $immmm )  $sett  UNION 'sett' STORE
                                        %> IFT
                                        $last 'pre' STORE
                                    %> FOREACH
                                %> IFTE
                            %> FOREACH
                            $sett SET-> 'res' STORE
                            { 'Result' true 'Data' $res }  
                        %>
                        <% { 'Result' false 'Exception' ERROR } %>
                        <% %>
                        TRY";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<string[]>>(script, true));
        }
        
        
        public static async Task<string[]> LostDataDevices(this WarpService warpService, DateTime fromDate,
            DateTime toDate, int distanceSplit = 20000, int secondsSplit = 300)
        {
            string script = "'" + WarpHelper.ConvertDate(fromDate) + @"' 'startDate' STORE
                            '" + WarpHelper.ConvertDate(toDate) + @"' 'endDate' STORE
                        " + distanceSplit + @" 'distance_split' STORE
                        " + secondsSplit + @" 'seconds_split' STORE

                        [ ] ->SET 'sett' STORE
                        <%
                            [ $readToken 'speed' { } $startDate $endDate ] FETCH
                            {
                                'distance.split' $distance_split
                                'label.split.number' 'distancesplit'
                            }
                            MOTIONSPLIT
                            <%
                                DUP
                                <% SIZE 1 == %>
                                <%
                                    DROP
                                %>
                                <%
                                    0 'pre' STORE
                                    <%
                                        DUP
                                        TICKS DUP 0 GET 'first' STORE DUP SIZE 1 - GET 'last' STORE
                                        LABELS 'imei' GET 'immmm' STORE
                                        <% $pre 0 != $first $pre - $seconds_split TOLONG 1000 * > AND %>
                                        <%
                                            ( $immmm )  $sett  UNION 'sett' STORE
                                        %> IFT
                                        $last 'pre' STORE
                                    %> FOREACH
                                %> IFTE
                            %> FOREACH
                            $sett SET-> 'res' STORE
                            { 'Result' true 'Data' $res }  
                        %>
                        <% { 'Result' false 'Exception' ERROR } %>
                        <% %>
                        TRY";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<string[]>>(script, true));
        }
        
        
        public static async Task<string[]> Satellite(this WarpService warpService, DateTime fromDate,
            DateTime toDate, int percentage = 95)
        {
            string script = WarpHelper.ConvertDate(fromDate) + @" 'startDate' STORE
                            " + WarpHelper.ConvertDate(toDate) + @" 'endDate' STORE
                        " + percentage + @" 'percentage' STORE
                        <%
                            [ 
                              $readToken
                              $startDate
                              $endDate
                              $percentage 
                            ] @report/SATELLITETHRESHOLD
                            [] 'list' STORE
                            <%
                                $list SWAP 0 GET +! 'list' STORE
                            %> FOREACH
                            { 'Result' true 'Data' $list }  
                        %>
                        <% { 'Result' false 'Exception' ERROR } %>
                        <% %>
                        TRY";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<string[]>>(script, true));
        }
        
        public static async Task<AverageSatellitesWarpResponseDto> AverageSatellites(this WarpService warpService, DateTime fromDate,
            DateTime toDate, string imei)
        {
            string script = WarpHelper.ConvertDate(fromDate) + @" 'startDate' STORE
                            " + WarpHelper.ConvertDate(toDate) + @" 'endDate' STORE
                            '" + imei + @"' 'imei' STORE
                            [ $readToken $imei $startDate $endDate ] @report/SATELLITEAVERAGE 'count' STORE 'sum' STORE
                            { 'Result' true 'Data' { 'Count' $count 'Sum' $sum }  }  
                        ";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<AverageSatellitesWarpResponseDto>>(script, true));
        }
        
        public static async Task<string[]> WalkingDead(this WarpService warpService, DateTime fromDate,
            DateTime toDate)
        {
            string script = WarpHelper.ConvertDate(fromDate) + @" 'startDate' STORE
                            " + WarpHelper.ConvertDate(toDate) + @" 'endDate' STORE
                        <%
                            [ 
                              $readToken
                              $startDate
                              $endDate
                            ] @report/WALKINGDEAD
                            'list' STORE
                            { 'Result' true 'Data' $list }  
                        %>
                        <% { 'Result' false 'Exception' ERROR } %>
                        <% %>
                        TRY";
            return WarpHelper.CheckWarpResponse(await warpService.ExecScript<WarpResponse<string[]>>(script, true));
        }
    }
}