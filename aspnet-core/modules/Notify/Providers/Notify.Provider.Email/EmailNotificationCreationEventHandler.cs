using Microsoft.Extensions.DependencyInjection;
using Notify.Notifications.UserNotifications;
using Notify.Notifications;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Domain.Entities.Events;

namespace Notify.Provider.Email;

public class EmailNotificationCreationEventHandler : NotificationCreationEventHandlerBase
{
    private readonly IServiceScopeFactory _serviceScopeFactory;

    public EmailNotificationCreationEventHandler(IServiceScopeFactory serviceScopeFactory)
    {
        _serviceScopeFactory = serviceScopeFactory;
    }

    protected override string NotificationMethod => NotifyProviderEmailConsts.NotificationMethodEmail;

    protected override async Task InternalHandleEventAsync(EntityCreatedEventData<UserNotification> eventData)
    {
        using var scope = _serviceScopeFactory.CreateScope();

        var backgroundJobManager = scope.ServiceProvider.GetRequiredService<IBackgroundJobManager>();

        await backgroundJobManager.EnqueueAsync(new EmailNotificationSendingJobArgs(eventData.Entity.Id));
    }
}
