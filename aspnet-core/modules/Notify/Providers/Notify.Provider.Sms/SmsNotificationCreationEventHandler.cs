using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Notify.Notifications;
using Notify.Notifications.UserNotifications;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Domain.Entities.Events;

namespace Notify.Provider.Sms;

public class SmsNotificationCreationEventHandler : NotificationCreationEventHandlerBase
{
    private readonly IServiceScopeFactory _serviceScopeFactory;

    public SmsNotificationCreationEventHandler(IServiceScopeFactory serviceScopeFactory)
    {
        _serviceScopeFactory = serviceScopeFactory;
    }
    
    protected override string NotificationMethod => NotifyProviderSmsConsts.NotificationMethodSms;

    protected override async Task InternalHandleEventAsync(EntityCreatedEventData<UserNotification> eventData)
    {
        using var scope = _serviceScopeFactory.CreateScope();

        var backgroundJobManager = scope.ServiceProvider.GetRequiredService<IBackgroundJobManager>();

        await backgroundJobManager.EnqueueAsync(new SmsUserNotificationSendingJobArgs(eventData.Entity.Id));
    }
}