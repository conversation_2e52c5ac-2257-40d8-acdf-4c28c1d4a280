namespace Notify.Provider.FCM;

public class NotifyProviderFCMConsts
{
    public const string NotificationMethodFCM = "FCM";

    public const string NotificationTitlePropertyName = "Title";

    public const string NotificationBodyPropertyName = "Body";

    public const string FailureReasonWhenDeviceTokenNotFound = "Receiver DeviceToken NotFound";

    public const string FailureReasonWhenFirebasethrowsException = "Firebase throws exception";
}
