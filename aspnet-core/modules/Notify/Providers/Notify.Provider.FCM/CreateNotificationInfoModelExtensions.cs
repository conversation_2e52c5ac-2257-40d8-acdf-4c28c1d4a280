using Volo.Abp.Data;

namespace Notify.Provider.FCM;

public static class CreateNotificationInfoModelExtensions
{
    public static void SetTitle(this CreateNotificationModel model, string text)
    {
        model.SetProperty(NotifyProviderFCMConsts.NotificationTitlePropertyName, text);
    }

    public static void SetBody(this CreateNotificationModel model, string text)
    {
        model.SetProperty(NotifyProviderFCMConsts.NotificationBodyPropertyName, text);
    }

    public static string GetTitle(this CreateNotificationModel model)
    {
        if (model.GetProperty(NotifyProviderFCMConsts.NotificationTitlePropertyName) is not string text)
            throw new NotImplementedException();

        return text;
    }

    public static string GetBody(this CreateNotificationModel model)
    {
        if (model.GetProperty(NotifyProviderFCMConsts.NotificationBodyPropertyName) is not string text)
            throw new NotImplementedException();

        return text;
    }
}
